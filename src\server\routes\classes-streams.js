const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get classes and streams overview
router.get('/', async (req, res) => {
  try {
    const { academic_year_id, level } = req.query;
    
    let whereClause = 'WHERE c.is_active = TRUE';
    let params = [];
    
    if (academic_year_id) {
      whereClause += ' AND c.academic_year_id = ?';
      params.push(academic_year_id);
    }
    
    if (level) {
      whereClause += ' AND el.code = ?';
      params.push(level);
    }

    const query = `
      SELECT
        c.id, c.name as class_name, c.current_enrollment, c.is_active,
        cl.id as class_level_id, cl.name as class_level_name, cl.sort_order,
        el.code as education_level_code, el.name as education_level_name,
        s.id as stream_id, s.name as stream_name, s.stream_type,
        COUNT(se.student_id) as actual_enrollment,
        GROUP_CONCAT(DISTINCT CASE
          WHEN el.code = 'o_level' THEN osub.short_name
          WHEN el.code = 'a_level' THEN asub.short_name
        END ORDER BY CASE
          WHEN el.code = 'o_level' THEN osub.name
          WHEN el.code = 'a_level' THEN asub.name
        END SEPARATOR ', ') as assigned_subjects
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams s ON c.stream_id = s.id
      LEFT JOIN student_enrollments se ON c.id = se.class_id
      LEFT JOIN class_subject_assignments csa ON c.id = csa.class_id
      LEFT JOIN o_level_subjects osub ON csa.subject_id = osub.id AND csa.subject_level = 'o_level'
      LEFT JOIN a_level_subjects asub ON csa.subject_id = asub.id AND csa.subject_level = 'a_level'
      ${whereClause}
      GROUP BY c.id, c.name, c.current_enrollment, c.is_active,
               cl.id, cl.name, cl.sort_order,
               el.code, el.name, s.id, s.name, s.stream_type
      ORDER BY cl.sort_order, c.name
    `;
    
    const result = await executeQuery(query, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get classes and streams error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve classes and streams'
    });
  }
});

// Get class details with students and subjects
router.get('/:classId/details', async (req, res) => {
  try {
    const { classId } = req.params;
    const { academic_year_id, term_id } = req.query;

    // Get class basic information
    const classQuery = `
      SELECT
        c.*,
        cl.name as class_level_name, cl.sort_order, cl.streams_optional,
        el.code as education_level_code, el.name as education_level_name,
        s.name as stream_name, s.stream_type
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams s ON c.stream_id = s.id
      WHERE c.id = ?
    `;
    
    const classResult = await executeQuery(classQuery, [classId]);
    
    if (!classResult.success || classResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    // Get enrolled students
    let studentWhereClause = 'WHERE se.class_id = ?';
    let studentParams = [classId];
    
    if (academic_year_id) {
      studentWhereClause += ' AND se.academic_year_id = ?';
      studentParams.push(academic_year_id);
    }
    
    if (term_id) {
      studentWhereClause += ' AND se.term_id = ?';
      studentParams.push(term_id);
    }

    const studentsQuery = `
      SELECT 
        s.id, s.admission_number, s.first_name, s.middle_name, s.last_name,
        s.gender, s.status, se.enrollment_date
      FROM student_enrollments se
      JOIN students s ON se.student_id = s.id
      ${studentWhereClause}
      ORDER BY s.admission_number
    `;
    
    const studentsResult = await executeQuery(studentsQuery, studentParams);

    // Get assigned subjects
    const subjectsQuery = `
      SELECT
        CASE
          WHEN csa.subject_level = 'o_level' THEN osub.id
          WHEN csa.subject_level = 'a_level' THEN asub.id
        END as id,
        CASE
          WHEN csa.subject_level = 'o_level' THEN osub.name
          WHEN csa.subject_level = 'a_level' THEN asub.name
        END as name,
        CASE
          WHEN csa.subject_level = 'o_level' THEN osub.short_name
          WHEN csa.subject_level = 'a_level' THEN asub.short_name
        END as short_name,
        csa.subject_level as level,
        CASE
          WHEN csa.subject_level = 'o_level' THEN osub.subject_type
          WHEN csa.subject_level = 'a_level' THEN asub.subject_type
        END as subject_type,
        csa.created_at as assigned_date,
        t.id as teacher_id, t.first_name as teacher_first_name, t.last_name as teacher_last_name
      FROM class_subject_assignments csa
      LEFT JOIN o_level_subjects osub ON csa.subject_id = osub.id AND csa.subject_level = 'o_level'
      LEFT JOIN a_level_subjects asub ON csa.subject_id = asub.id AND csa.subject_level = 'a_level'
      LEFT JOIN teacher_assignments ta ON csa.class_id = ta.class_id AND csa.subject_id = ta.subject_id AND csa.subject_level = ta.subject_level
      LEFT JOIN teachers t ON ta.teacher_id = t.id
      WHERE csa.class_id = ?
      ORDER BY CASE
        WHEN csa.subject_level = 'o_level' THEN osub.name
        WHEN csa.subject_level = 'a_level' THEN asub.name
      END
    `;
    
    const subjectsResult = await executeQuery(subjectsQuery, [classId]);

    const classDetails = {
      class_info: classResult.data[0],
      students: studentsResult.success ? studentsResult.data : [],
      subjects: subjectsResult.success ? subjectsResult.data : []
    };

    res.json({
      success: true,
      data: classDetails
    });

  } catch (error) {
    console.error('Get class details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve class details'
    });
  }
});

// Assign subjects to class
router.post('/:classId/assign-subjects', async (req, res) => {
  try {
    const { classId } = req.params;
    const { subject_ids, academic_year_id } = req.body;

    if (!subject_ids || !Array.isArray(subject_ids) || subject_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Subject IDs array is required'
      });
    }

    if (!academic_year_id) {
      return res.status(400).json({
        success: false,
        message: 'Academic year ID is required'
      });
    }

    // Check if class exists
    const classCheck = await executeQuery('SELECT id FROM classes WHERE id = ?', [classId]);
    if (!classCheck.success || classCheck.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    // Remove existing assignments for this class and academic year
    await executeQuery(
      'DELETE FROM class_subject_assignments WHERE class_id = ? AND academic_year_id = ?',
      [classId, academic_year_id]
    );

    // Insert new assignments
    const assignmentPromises = subject_ids.map(subjectId => {
      const assignQuery = `
        INSERT INTO class_subject_assignments (
          class_id, subject_id, academic_year_id, created_at, updated_at
        ) VALUES (?, ?, ?, NOW(), NOW())
      `;
      return executeQuery(assignQuery, [classId, subjectId, academic_year_id]);
    });

    const results = await Promise.all(assignmentPromises);
    
    // Check if all assignments were successful
    const failedAssignments = results.filter(result => !result.success);
    if (failedAssignments.length > 0) {
      throw new Error('Some subject assignments failed');
    }

    res.json({
      success: true,
      message: 'Subjects assigned to class successfully',
      data: {
        class_id: classId,
        subjects_assigned: subject_ids.length,
        academic_year_id
      }
    });

  } catch (error) {
    console.error('Assign subjects to class error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign subjects to class'
    });
  }
});

// Get class enrollment statistics
router.get('/enrollment-stats', async (req, res) => {
  try {
    const { academic_year_id, term_id } = req.query;
    
    let whereClause = 'WHERE c.is_active = TRUE';
    let params = [];
    
    if (academic_year_id) {
      whereClause += ' AND c.academic_year_id = ?';
      params.push(academic_year_id);
    }
    
    if (term_id) {
      whereClause += ' AND se.term_id = ?';
      params.push(term_id);
    }

    const statsQuery = `
      SELECT
        el.name as education_level_name,
        el.code as education_level_code,
        COUNT(DISTINCT c.id) as total_classes,
        COUNT(DISTINCT se.student_id) as total_students,
        AVG(class_enrollment.student_count) as average_class_size,
        MAX(class_enrollment.student_count) as largest_class_size,
        MIN(class_enrollment.student_count) as smallest_class_size
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN student_enrollments se ON c.id = se.class_id
      LEFT JOIN (
        SELECT
          class_id,
          COUNT(student_id) as student_count
        FROM student_enrollments
        ${term_id ? 'WHERE term_id = ?' : 'WHERE 1=1'}
        GROUP BY class_id
      ) class_enrollment ON c.id = class_enrollment.class_id
      ${whereClause}
      GROUP BY el.id, el.name, el.code
      ORDER BY el.sort_order
    `;
    
    const enrollmentParams = term_id ? [term_id, ...params] : params;
    const result = await executeQuery(statsQuery, enrollmentParams);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get enrollment stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve enrollment statistics'
    });
  }
});

// Get stream distribution
router.get('/stream-distribution', async (req, res) => {
  try {
    const { academic_year_id, level_type } = req.query;
    
    let whereClause = 'WHERE c.is_active = TRUE AND s.is_active = TRUE';
    let params = [];
    
    if (academic_year_id) {
      whereClause += ' AND c.academic_year_id = ?';
      params.push(academic_year_id);
    }
    
    if (level_type) {
      whereClause += ' AND el.code = ?';
      params.push(level_type);
    }

    const distributionQuery = `
      SELECT
        s.name as stream_name,
        s.stream_type,
        el.code as education_level_code,
        el.name as education_level_name,
        COUNT(DISTINCT c.id) as classes_count,
        COUNT(DISTINCT se.student_id) as students_count
      FROM streams s
      JOIN classes c ON s.id = c.stream_id
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN student_enrollments se ON c.id = se.class_id
      ${whereClause}
      GROUP BY s.id, s.name, s.stream_type, el.code, el.name
      ORDER BY el.sort_order, s.stream_type, s.name
    `;
    
    const result = await executeQuery(distributionQuery, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get stream distribution error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve stream distribution'
    });
  }
});

module.exports = router;
