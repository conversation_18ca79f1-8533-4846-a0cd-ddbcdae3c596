// AIMS - UI Helper Functions for Tailwind CSS
// Modern UI interactions and utilities

// Toggle password visibility
function togglePassword() {
  const passwordInput = document.getElementById('password');
  const toggleIcon = document.getElementById('password-toggle-icon');
  
  if (passwordInput.type === 'password') {
    passwordInput.type = 'text';
    toggleIcon.className = 'fas fa-eye-slash';
  } else {
    passwordInput.type = 'password';
    toggleIcon.className = 'fas fa-eye';
  }
}

// Toggle sidebar
function toggleSidebar() {
  const sidebar = document.getElementById('sidebar');
  const toggleBtn = sidebar.querySelector('.sidebar-toggle-btn i');
  const brandText = sidebar.querySelector('.sidebar-brand-text');
  
  if (sidebar.classList.contains('w-72')) {
    // Collapse sidebar
    sidebar.classList.remove('w-72');
    sidebar.classList.add('w-16');
    toggleBtn.className = 'fas fa-chevron-right text-gray-600';
    brandText.classList.add('hidden');
  } else {
    // Expand sidebar
    sidebar.classList.remove('w-16');
    sidebar.classList.add('w-72');
    toggleBtn.className = 'fas fa-chevron-left text-gray-600';
    brandText.classList.remove('hidden');
  }
}

// Toggle mobile sidebar
function toggleMobileSidebar() {
  const sidebar = document.getElementById('sidebar');
  sidebar.classList.toggle('hidden');
}

// Toggle user menu
function toggleUserMenu() {
  const dropdown = document.getElementById('user-dropdown');
  dropdown.classList.toggle('hidden');
  
  // Close dropdown when clicking outside
  if (!dropdown.classList.contains('hidden')) {
    document.addEventListener('click', function closeDropdown(e) {
      if (!dropdown.contains(e.target) && !e.target.closest('.relative')) {
        dropdown.classList.add('hidden');
        document.removeEventListener('click', closeDropdown);
      }
    });
  }
}

// Enhanced notification system with modern design
function showNotification(message, type = 'info', duration = 5000, actions = []) {
  const container = document.getElementById('notification-container');
  if (!container) return;

  const notification = document.createElement('div');
  const id = 'notification-' + Date.now();
  notification.id = id;

  // Enhanced notification styles based on type
  const typeConfig = {
    success: {
      bgColor: 'bg-white',
      borderColor: 'border-success-200',
      textColor: 'text-gray-900',
      icon: 'fas fa-check-circle',
      iconBg: 'bg-success-100 text-success-600'
    },
    error: {
      bgColor: 'bg-white',
      borderColor: 'border-danger-200',
      textColor: 'text-gray-900',
      icon: 'fas fa-exclamation-circle',
      iconBg: 'bg-danger-100 text-danger-600'
    },
    warning: {
      bgColor: 'bg-white',
      borderColor: 'border-warning-200',
      textColor: 'text-gray-900',
      icon: 'fas fa-exclamation-triangle',
      iconBg: 'bg-warning-100 text-warning-600'
    },
    info: {
      bgColor: 'bg-white',
      borderColor: 'border-info-200',
      textColor: 'text-gray-900',
      icon: 'fas fa-info-circle',
      iconBg: 'bg-info-100 text-info-600'
    }
  };

  const config = typeConfig[type] || typeConfig.info;

  notification.className = `${config.bgColor} ${config.borderColor} ${config.textColor} border-2 rounded-xl shadow-hard p-4 transform translate-x-full transition-all duration-300 ease-out backdrop-blur-sm animate-slide-in-right`;

  const actionsHtml = actions.length > 0 ? `
    <div class="flex space-x-2 mt-3">
      ${actions.map(action => `
        <button onclick="${action.onclick}" class="${action.class || 'px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200'}">${action.text}</button>
      `).join('')}
    </div>
  ` : '';

  notification.innerHTML = `
    <div class="flex items-start space-x-3">
      <div class="w-8 h-8 ${config.iconBg} rounded-lg flex items-center justify-center flex-shrink-0">
        <i class="${config.icon} text-sm"></i>
      </div>
      <div class="flex-1 min-w-0">
        <div class="text-sm font-medium">${message}</div>
        ${actionsHtml}
      </div>
      <button onclick="removeNotification('${id}')" class="text-gray-400 hover:text-gray-600 transition-colors duration-200 flex-shrink-0">
        <i class="fas fa-times text-sm"></i>
      </button>
    </div>
  `;

  container.appendChild(notification);

  // Animate in
  setTimeout(() => {
    notification.classList.remove('translate-x-full');
  }, 100);

  // Auto remove
  if (duration > 0) {
    setTimeout(() => {
      removeNotification(id);
    }, duration);
  }

  return id;
}

// Enhanced notification removal
function removeNotification(id) {
  const notification = document.getElementById(id);
  if (notification) {
    notification.classList.add('translate-x-full', 'opacity-0');
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 300);
  }
}

// Progress notification for long operations
function showProgressNotification(message, progress = 0) {
  const id = showNotification(`
    <div class="flex items-center space-x-3">
      <span>${message}</span>
      <div class="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
        <div class="h-full bg-primary-500 rounded-full transition-all duration-300" style="width: ${progress}%"></div>
      </div>
      <span class="text-xs text-gray-500">${progress}%</span>
    </div>
  `, 'info', 0);

  return {
    id,
    update: (newProgress, newMessage) => {
      const notification = document.getElementById(id);
      if (notification) {
        const progressBar = notification.querySelector('.bg-primary-500');
        const progressText = notification.querySelector('.text-xs');
        const messageEl = notification.querySelector('span');

        if (progressBar) progressBar.style.width = `${newProgress}%`;
        if (progressText) progressText.textContent = `${newProgress}%`;
        if (newMessage && messageEl) messageEl.textContent = newMessage;
      }
    },
    complete: (finalMessage = 'Completed') => {
      removeNotification(id);
      showNotification(finalMessage, 'success');
    }
  };
}

// Enhanced modal system with modern design
function showModal(title, content, actions = [], options = {}) {
  const container = document.getElementById('modal-container');

  const {
    size = 'md',
    closable = true,
    backdrop = true,
    animation = 'slide-up'
  } = options;

  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-full mx-4'
  };

  const modal = document.createElement('div');
  modal.className = `fixed inset-0 z-50 flex items-center justify-center p-4 modal-backdrop`;

  const actionsHtml = actions.map(action =>
    `<button onclick="${action.onclick}" class="${action.class || 'bg-gray-500 hover:bg-gray-600'} text-white px-4 py-2 rounded-xl font-medium transition-all duration-200 transform hover:scale-105">${action.text}</button>`
  ).join('');

  modal.innerHTML = `
    <div class="bg-white rounded-2xl shadow-hard ${sizeClasses[size]} w-full animate-modal-in border border-gray-200 overflow-hidden relative z-10">
      <!-- Modal Header -->
      ${title ? `
        <div class="relative overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-r from-primary-50 to-secondary-50"></div>
          <div class="relative px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h3 class="text-xl font-bold text-gray-900 flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                  <i class="fas fa-window-maximize text-white text-sm"></i>
                </div>
                <span>${title}</span>
              </h3>
              ${closable ? `
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-2 rounded-lg hover:bg-gray-100">
                  <i class="fas fa-times"></i>
                </button>
              ` : ''}
            </div>
          </div>
        </div>
      ` : ''}

      <!-- Modal Content -->
      <div class="px-6 py-6 max-h-96 overflow-y-auto custom-scrollbar">
        ${content}
      </div>

      <!-- Modal Actions -->
      ${actions.length > 0 ? `
        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-center space-x-3">
          ${actionsHtml}
        </div>
      ` : ''}
    </div>
  `;

  container.innerHTML = modal.outerHTML;
  container.classList.remove('hidden');

  // Close on backdrop click
  if (backdrop && closable) {
    container.addEventListener('click', (e) => {
      // Check if clicked on the backdrop (modal container or the backdrop div)
      if (e.target === container || e.target.classList.contains('modal-backdrop')) {
        closeModal();
      }
    });
  }

  // Close on Escape key
  if (closable) {
    document.addEventListener('keydown', function escapeHandler(e) {
      if (e.key === 'Escape') {
        closeModal();
        document.removeEventListener('keydown', escapeHandler);
      }
    });
  }
}

// Enhanced confirmation modal
function showConfirm(message, onConfirm, onCancel = null, options = {}) {
  const {
    title = 'Confirm Action',
    confirmText = 'Confirm',
    cancelText = 'Cancel',
    type = 'warning'
  } = options;

  const typeConfig = {
    warning: {
      icon: 'fas fa-exclamation-triangle',
      iconBg: 'bg-warning-100 text-warning-600',
      confirmClass: 'bg-warning-500 hover:bg-warning-600'
    },
    danger: {
      icon: 'fas fa-exclamation-circle',
      iconBg: 'bg-danger-100 text-danger-600',
      confirmClass: 'bg-danger-500 hover:bg-danger-600'
    },
    info: {
      icon: 'fas fa-info-circle',
      iconBg: 'bg-info-100 text-info-600',
      confirmClass: 'bg-info-500 hover:bg-info-600'
    }
  };

  const config = typeConfig[type] || typeConfig.warning;

  showModal(title, `
    <div class="text-center">
      <div class="w-16 h-16 ${config.iconBg} rounded-full flex items-center justify-center mx-auto mb-4">
        <i class="${config.icon} text-2xl"></i>
      </div>
      <p class="text-gray-700 text-lg">${message}</p>
    </div>
  `, [
    {
      text: cancelText,
      onclick: `closeModal(); ${onCancel ? onCancel : ''}`,
      class: 'bg-gray-500 hover:bg-gray-600'
    },
    {
      text: confirmText,
      onclick: `closeModal(); ${onConfirm}`,
      class: config.confirmClass
    }
  ], { size: 'sm' });
}

// Enhanced modal closing with animation
function closeModal() {
  const container = document.getElementById('modal-container');
  const backdrop = container.querySelector('.modal-backdrop');
  const modalContent = container.querySelector('.animate-modal-in');

  if (backdrop && modalContent) {
    // Add closing animations
    backdrop.classList.add('closing');
    modalContent.classList.remove('animate-modal-in');
    modalContent.classList.add('animate-modal-out');

    setTimeout(() => {
      container.innerHTML = '';
      container.classList.add('hidden');
    }, 200);
  } else {
    container.innerHTML = '';
    container.classList.add('hidden');
  }
}

// Loading overlay functions
function showLoadingOverlay(message = 'Loading...') {
  const overlay = document.getElementById('loading-overlay');
  if (overlay) {
    overlay.querySelector('p').textContent = message;
    overlay.classList.remove('hidden');
  }
}

function hideLoadingOverlay() {
  const overlay = document.getElementById('loading-overlay');
  if (overlay) {
    overlay.classList.add('hidden');
  }
}

// Skeleton loader for content
function showSkeletonLoader(container, type = 'card') {
  const skeletons = {
    card: `
      <div class="animate-pulse">
        <div class="bg-gray-200 rounded-xl h-48 mb-4"></div>
        <div class="space-y-3">
          <div class="bg-gray-200 rounded h-4 w-3/4"></div>
          <div class="bg-gray-200 rounded h-4 w-1/2"></div>
          <div class="bg-gray-200 rounded h-4 w-5/6"></div>
        </div>
      </div>
    `,
    table: `
      <div class="animate-pulse">
        <div class="bg-gray-200 rounded h-8 mb-4"></div>
        ${Array(5).fill().map(() => `
          <div class="flex space-x-4 mb-3">
            <div class="bg-gray-200 rounded h-6 w-1/4"></div>
            <div class="bg-gray-200 rounded h-6 w-1/3"></div>
            <div class="bg-gray-200 rounded h-6 w-1/4"></div>
            <div class="bg-gray-200 rounded h-6 w-1/6"></div>
          </div>
        `).join('')}
      </div>
    `,
    list: `
      <div class="animate-pulse space-y-4">
        ${Array(6).fill().map(() => `
          <div class="flex items-center space-x-4">
            <div class="bg-gray-200 rounded-full h-12 w-12"></div>
            <div class="flex-1 space-y-2">
              <div class="bg-gray-200 rounded h-4 w-3/4"></div>
              <div class="bg-gray-200 rounded h-3 w-1/2"></div>
            </div>
          </div>
        `).join('')}
      </div>
    `
  };

  if (container) {
    container.innerHTML = skeletons[type] || skeletons.card;
  }
}

// Loading state helpers
function showLoadingButton(button, text = 'Loading...') {
  button.disabled = true;
  button.innerHTML = `
    <div class="flex items-center space-x-2">
      <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
      <span>${text}</span>
    </div>
  `;
}

function hideLoadingButton(button, originalText) {
  button.disabled = false;
  button.innerHTML = originalText;
}

// Search functionality
function initializeGlobalSearch() {
  const searchInput = document.getElementById('global-search');
  const clearButton = document.getElementById('search-clear');
  
  if (searchInput) {
    searchInput.addEventListener('input', function() {
      if (this.value.length > 0) {
        clearButton.classList.remove('hidden');
      } else {
        clearButton.classList.add('hidden');
      }
    });
  }
}

function clearGlobalSearch() {
  const searchInput = document.getElementById('global-search');
  const clearButton = document.getElementById('search-clear');
  
  if (searchInput) {
    searchInput.value = '';
    clearButton.classList.add('hidden');
    searchInput.focus();
  }
}

// Enhanced sidebar toggle with animation
function toggleSidebar() {
  const sidebar = document.getElementById('sidebar');
  const toggleBtn = sidebar.querySelector('.sidebar-toggle-btn i');
  const brandText = sidebar.querySelector('.sidebar-brand-text');
  const quickStats = sidebar.querySelector('.grid.grid-cols-2');

  if (sidebar.classList.contains('w-80')) {
    // Collapse sidebar
    sidebar.classList.remove('w-80');
    sidebar.classList.add('w-20');
    toggleBtn.className = 'fas fa-chevron-right text-white group-hover:scale-110 transition-transform duration-200';
    if (brandText) brandText.classList.add('hidden');
    if (quickStats) quickStats.classList.add('hidden');
  } else {
    // Expand sidebar
    sidebar.classList.remove('w-20');
    sidebar.classList.add('w-80');
    toggleBtn.className = 'fas fa-chevron-left text-white group-hover:scale-110 transition-transform duration-200';
    if (brandText) brandText.classList.remove('hidden');
    if (quickStats) quickStats.classList.remove('hidden');
  }
}

// Enhanced user menu toggle
function toggleUserMenu() {
  const dropdown = document.getElementById('user-dropdown');
  dropdown.classList.toggle('hidden');

  // Close dropdown when clicking outside
  if (!dropdown.classList.contains('hidden')) {
    document.addEventListener('click', function closeDropdown(e) {
      if (!dropdown.contains(e.target) && !e.target.closest('.relative')) {
        dropdown.classList.add('hidden');
        document.removeEventListener('click', closeDropdown);
      }
    });
  }
}


// Initialize UI helpers when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  initializeGlobalSearch();
  // Keyboard shortcuts removed as requested

  // Initialize tooltips
  initializeTooltips();

  // Update time display
  updateTimeDisplay();
  setInterval(updateTimeDisplay, 60000); // Update every minute
});

// Tooltip system
function initializeTooltips() {
  const tooltipElements = document.querySelectorAll('[title]');
  tooltipElements.forEach(element => {
    element.addEventListener('mouseenter', showTooltip);
    element.addEventListener('mouseleave', hideTooltip);
  });
}

function showTooltip(e) {
  const text = e.target.getAttribute('title');
  if (!text) return;

  // Remove title to prevent default tooltip
  e.target.setAttribute('data-tooltip', text);
  e.target.removeAttribute('title');

  const tooltip = document.createElement('div');
  tooltip.id = 'custom-tooltip';
  tooltip.className = 'absolute z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg pointer-events-none animate-fade-in';
  tooltip.textContent = text;

  document.body.appendChild(tooltip);

  // Position tooltip
  const rect = e.target.getBoundingClientRect();
  tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
  tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
}

function hideTooltip(e) {
  const tooltip = document.getElementById('custom-tooltip');
  if (tooltip) {
    tooltip.remove();
  }

  // Restore title
  const tooltipText = e.target.getAttribute('data-tooltip');
  if (tooltipText) {
    e.target.setAttribute('title', tooltipText);
    e.target.removeAttribute('data-tooltip');
  }
}

// Update time display in navbar
function updateTimeDisplay() {
  const dateElement = document.getElementById('navbar-date');
  if (dateElement) {
    const now = new Date();
    const options = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    dateElement.textContent = now.toLocaleDateString('en-UG', options);
  }
}

// Enhanced menu action functions
function showNotifications() {
  showModal('Notifications', `
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <h4 class="font-semibold text-gray-900">Recent Notifications</h4>
        <button class="text-sm text-primary-600 hover:text-primary-800">Mark all as read</button>
      </div>

      <div class="space-y-3 max-h-64 overflow-y-auto">
        <div class="flex items-start space-x-3 p-3 bg-primary-50 rounded-lg border border-primary-200">
          <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-user-plus text-primary-600 text-sm"></i>
          </div>
          <div class="flex-1">
            <p class="text-sm font-medium text-gray-900">New student enrolled</p>
            <p class="text-xs text-gray-600">John Doe has been enrolled in S2A</p>
            <p class="text-xs text-gray-500 mt-1">2 minutes ago</p>
          </div>
        </div>

        <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
          <div class="w-8 h-8 bg-secondary-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-clipboard-check text-secondary-600 text-sm"></i>
          </div>
          <div class="flex-1">
            <p class="text-sm font-medium text-gray-900">Assessment completed</p>
            <p class="text-xs text-gray-600">Mathematics Term 2 assessment graded</p>
            <p class="text-xs text-gray-500 mt-1">1 hour ago</p>
          </div>
        </div>

        <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
          <div class="w-8 h-8 bg-accent-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-calendar text-accent-600 text-sm"></i>
          </div>
          <div class="flex-1">
            <p class="text-sm font-medium text-gray-900">Upcoming deadline</p>
            <p class="text-xs text-gray-600">Term 2 reports due in 3 days</p>
            <p class="text-xs text-gray-500 mt-1">3 hours ago</p>
          </div>
        </div>
      </div>
    </div>
  `, [
    { text: 'View All', onclick: 'closeModal(); loadPage("notifications")', class: 'bg-primary-500 hover:bg-primary-600' },
    { text: 'Close', onclick: 'closeModal()', class: 'bg-gray-500 hover:bg-gray-600' }
  ], { size: 'lg' });
}

function showQuickAdd() {
  showModal('Quick Add', `
    <div class="grid grid-cols-2 gap-4">
      <button onclick="closeModal(); loadPage('register-student')" class="p-4 border-2 border-dashed border-primary-300 rounded-xl hover:border-primary-500 hover:bg-primary-50 transition-all duration-200 group">
        <div class="text-center">
          <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:bg-primary-200 transition-colors duration-200">
            <i class="fas fa-user-plus text-primary-600 text-xl"></i>
          </div>
          <h4 class="font-semibold text-gray-900 mb-1">Add Student</h4>
          <p class="text-sm text-gray-600">Register new student</p>
        </div>
      </button>

      <button onclick="closeModal(); loadPage('register-teacher')" class="p-4 border-2 border-dashed border-secondary-300 rounded-xl hover:border-secondary-500 hover:bg-secondary-50 transition-all duration-200 group">
        <div class="text-center">
          <div class="w-12 h-12 bg-secondary-100 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:bg-secondary-200 transition-colors duration-200">
            <i class="fas fa-chalkboard-teacher text-secondary-600 text-xl"></i>
          </div>
          <h4 class="font-semibold text-gray-900 mb-1">Add Teacher</h4>
          <p class="text-sm text-gray-600">Register new teacher</p>
        </div>
      </button>

      <button onclick="closeModal(); loadPage('create-class')" class="p-4 border-2 border-dashed border-accent-300 rounded-xl hover:border-accent-500 hover:bg-accent-50 transition-all duration-200 group">
        <div class="text-center">
          <div class="w-12 h-12 bg-accent-100 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:bg-accent-200 transition-colors duration-200">
            <i class="fas fa-users text-accent-600 text-xl"></i>
          </div>
          <h4 class="font-semibold text-gray-900 mb-1">Create Class</h4>
          <p class="text-sm text-gray-600">Setup new class</p>
        </div>
      </button>

      <button onclick="closeModal(); loadPage('create-assessment')" class="p-4 border-2 border-dashed border-warning-300 rounded-xl hover:border-warning-500 hover:bg-warning-50 transition-all duration-200 group">
        <div class="text-center">
          <div class="w-12 h-12 bg-warning-100 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:bg-warning-200 transition-colors duration-200">
            <i class="fas fa-clipboard-list text-warning-600 text-xl"></i>
          </div>
          <h4 class="font-semibold text-gray-900 mb-1">New Assessment</h4>
          <p class="text-sm text-gray-600">Create assessment</p>
        </div>
      </button>
    </div>
  `, [
    { text: 'Cancel', onclick: 'closeModal()', class: 'bg-gray-500 hover:bg-gray-600' }
  ], { size: 'lg' });
}

function showHelp() {
  showModal('Help & Support', `
    <div class="space-y-6">
      <div class="text-center">
        <div class="w-16 h-16 bg-info-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-question-circle text-info-600 text-2xl"></i>
        </div>
        <h4 class="text-lg font-semibold text-gray-900 mb-2">How can we help you?</h4>
        <p class="text-gray-600">Choose from the options below or contact support</p>
      </div>

      <div class="grid grid-cols-1 gap-3">
        <button class="flex items-center space-x-3 p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
          <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-book text-primary-600 text-sm"></i>
          </div>
          <div>
            <h5 class="font-medium text-gray-900">User Guide</h5>
            <p class="text-sm text-gray-600">Complete system documentation</p>
          </div>
        </button>

        <button class="flex items-center space-x-3 p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
          <div class="w-8 h-8 bg-secondary-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-video text-secondary-600 text-sm"></i>
          </div>
          <div>
            <h5 class="font-medium text-gray-900">Video Tutorials</h5>
            <p class="text-sm text-gray-600">Step-by-step video guides</p>
          </div>
        </button>

        <button class="flex items-center space-x-3 p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
          <div class="w-8 h-8 bg-accent-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-keyboard text-accent-600 text-sm"></i>
          </div>
          <div>
            <h5 class="font-medium text-gray-900">Keyboard Shortcuts</h5>
            <p class="text-sm text-gray-600">Speed up your workflow</p>
          </div>
        </button>
      </div>

      <div class="border-t pt-4">
        <p class="text-sm text-gray-600 mb-3">Need direct assistance?</p>
        <div class="flex space-x-3">
          <button class="flex-1 bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
            <i class="fas fa-envelope mr-2"></i>Email Support
          </button>
          <button class="flex-1 bg-secondary-500 hover:bg-secondary-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
            <i class="fas fa-phone mr-2"></i>Call Support
          </button>
        </div>
      </div>
    </div>
  `, [
    { text: 'Close', onclick: 'closeModal()', class: 'bg-gray-500 hover:bg-gray-600' }
  ], { size: 'lg' });
}

function showProfile() {
  showNotification('Opening profile page...', 'info');
  setTimeout(() => {
    loadPage('profile');
  }, 500);
}

function showSettings() {
  showNotification('Opening settings...', 'info');
  setTimeout(() => {
    loadPage('settings');
  }, 500);
}

function showPreferences() {
  showNotification('Opening preferences...', 'info');
  setTimeout(() => {
    loadPage('preferences');
  }, 500);
}

function showAbout() {
  showModal('About AIMS', 
    `<div class="text-center">
      <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
        <span class="text-white font-bold text-xl">A</span>
      </div>
      <h4 class="text-lg font-semibold mb-2">AIMS v2.0</h4>
      <p class="text-gray-600 mb-4">Academic Information Management System</p>
      <p class="text-sm text-gray-500">© 2025 Nyabikoni Secondary School</p>
    </div>`, 
    [{ text: 'Close', onclick: 'closeModal()', class: 'bg-gray-500 hover:bg-gray-600' }]
  );
}

function showForgotPassword() {
  showModal('Reset Password', `
    <div class="text-center mb-6">
      <div class="w-16 h-16 bg-warning-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
        <i class="fas fa-key text-warning-600 text-2xl"></i>
      </div>
      <h4 class="text-lg font-semibold text-gray-900 mb-2">Forgot your password?</h4>
      <p class="text-gray-600">Enter your username and we'll send you a reset link</p>
    </div>

    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
        <input type="text" id="reset-username" placeholder="Enter your username"
               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200">
      </div>

      <div class="bg-info-50 border border-info-200 rounded-lg p-4">
        <div class="flex items-start space-x-3">
          <i class="fas fa-info-circle text-info-600 mt-0.5"></i>
          <div class="text-sm text-info-700">
            <p class="font-medium mb-1">Need help?</p>
            <p>Contact your system administrator if you don't remember your username.</p>
          </div>
        </div>
      </div>
    </div>
  `, [
    { text: 'Cancel', onclick: 'closeModal()', class: 'bg-gray-500 hover:bg-gray-600' },
    { text: 'Send Reset Link', onclick: 'handlePasswordReset()', class: 'bg-primary-500 hover:bg-primary-600' }
  ], { size: 'md' });
}

function handlePasswordReset() {
  const username = document.getElementById('reset-username').value;
  if (!username.trim()) {
    showNotification('Please enter your username', 'warning');
    return;
  }

  closeModal();
  showNotification('Password reset instructions sent to your email', 'success');
}

function showLoginHelp() {
  showModal('Login Help', `
    <div class="space-y-6">
      <div class="text-center">
        <div class="w-16 h-16 bg-info-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-life-ring text-info-600 text-2xl"></i>
        </div>
        <h4 class="text-lg font-semibold text-gray-900 mb-2">Need help logging in?</h4>
      </div>

      <div class="space-y-4">
        <div class="bg-gray-50 rounded-lg p-4">
          <h5 class="font-semibold text-gray-900 mb-2">Default Login Credentials</h5>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">System Admin:</span>
              <span class="font-mono bg-white px-2 py-1 rounded">admin / admin123</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Class Teacher:</span>
              <span class="font-mono bg-white px-2 py-1 rounded">teacher1 / teacher123</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Subject Teacher:</span>
              <span class="font-mono bg-white px-2 py-1 rounded">subject1 / subject123</span>
            </div>
          </div>
        </div>

        <div class="bg-warning-50 border border-warning-200 rounded-lg p-4">
          <div class="flex items-start space-x-3">
            <i class="fas fa-exclamation-triangle text-warning-600 mt-0.5"></i>
            <div class="text-sm text-warning-700">
              <p class="font-medium mb-1">Security Notice</p>
              <p>Please change default passwords after first login for security.</p>
            </div>
          </div>
        </div>

        <div class="border-t pt-4">
          <h5 class="font-semibold text-gray-900 mb-3">Contact Administrator</h5>
          <div class="grid grid-cols-2 gap-3">
            <div class="text-center p-3 bg-primary-50 rounded-lg">
              <i class="fas fa-phone text-primary-600 mb-2"></i>
              <p class="text-sm font-medium text-gray-900">Phone</p>
              <p class="text-xs text-gray-600">+256 772 655 176</p>
            </div>
            <div class="text-center p-3 bg-secondary-50 rounded-lg">
              <i class="fas fa-envelope text-secondary-600 mb-2"></i>
              <p class="text-sm font-medium text-gray-900">Email</p>
              <p class="text-xs text-gray-600"><EMAIL></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  `, [
    { text: 'Close', onclick: 'closeModal()', class: 'bg-gray-500 hover:bg-gray-600' }
  ], { size: 'lg' });
}

// Quick login functions for demo
function quickLogin(type) {
  const credentials = {
    admin: { username: 'admin', password: 'admin123' },
    teacher: { username: 'teacher1', password: 'teacher123' },
    subject: { username: 'subject1', password: 'subject123' }
  };

  const cred = credentials[type];
  if (cred) {
    document.getElementById('username').value = cred.username;
    document.getElementById('password').value = cred.password;
    showNotification(`Demo credentials filled for ${type}`, 'info', 2000);
  }
}

// Page refresh function
function refreshPage() {
  showNotification('Refreshing page...', 'info', 1000);
  setTimeout(() => {
    location.reload();
  }, 500);
}

// Enhanced loading text update
function updateLoadingText(text) {
  const loadingMessage = document.getElementById('loading-message');
  const loadingStatus = document.getElementById('loading-status');

  if (loadingMessage) {
    loadingMessage.textContent = text;
  }
  if (loadingStatus) {
    loadingStatus.textContent = text;
  }
}

// Enhanced password toggle functionality
function togglePassword() {
  const passwordField = document.getElementById('password');
  const toggleIcon = document.getElementById('password-toggle-icon');

  if (passwordField && toggleIcon) {
    if (passwordField.type === 'password') {
      passwordField.type = 'text';
      toggleIcon.className = 'fas fa-eye-slash';
    } else {
      passwordField.type = 'password';
      toggleIcon.className = 'fas fa-eye';
    }
  }
}

// Enhanced breadcrumb update
function updateBreadcrumb(path) {
  const breadcrumbPath = document.getElementById('breadcrumb-path');
  if (breadcrumbPath) {
    const pathParts = path.split(' / ');
    const breadcrumbHtml = pathParts.map((part, index) => {
      if (index === 0) {
        return `<i class="fas fa-home text-primary-500"></i><span>${part}</span>`;
      } else if (index === pathParts.length - 1) {
        return `<i class="fas fa-chevron-right text-gray-400 text-xs"></i><span class="text-gray-900 font-medium">${part}</span>`;
      } else {
        return `<i class="fas fa-chevron-right text-gray-400 text-xs"></i><span>${part}</span>`;
      }
    }).join('');

    breadcrumbPath.innerHTML = breadcrumbHtml;
  }
}

// Enhanced page loading with immediate dashboard access
function loadDashboard() {
  updateLoadingText('Loading dashboard...');
  updateBreadcrumb('Home / Dashboard');

  // Load dashboard immediately
  if (typeof loadPage === 'function') {
    loadPage('dashboard');
  } else {
    // Use the main loadDashboard function from main.js
    if (window.loadDashboard && window.loadDashboard !== loadDashboard) {
      window.loadDashboard();
    }
  }
}

// Enhanced about modal
function showAbout() {
  showModal('About AIMS', `
    <div class="text-center">
      <div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-glow">
        <i class="fas fa-graduation-cap text-3xl text-white"></i>
      </div>

      <h4 class="text-2xl font-bold text-gray-900 mb-4">Academic Information Management System</h4>

      <div class="space-y-4 text-left">
        <div class="bg-gray-50 rounded-lg p-4">
          <h5 class="font-semibold text-gray-900 mb-2">Version Information</h5>
          <div class="grid grid-cols-2 gap-2 text-sm">
            <div class="text-gray-600">Version:</div>
            <div class="font-medium">2.0.0</div>
            <div class="text-gray-600">Build:</div>
            <div class="font-medium">2025.06.14</div>
            <div class="text-gray-600">Platform:</div>
            <div class="font-medium">Electron + Node.js</div>
          </div>
        </div>

        <div class="bg-primary-50 rounded-lg p-4 border border-primary-200">
          <h5 class="font-semibold text-primary-900 mb-2">School Information</h5>
          <div class="text-sm text-primary-800">
            <p class="font-medium">Nyabikoni Secondary School</p>
            <p>Academic Year 2025 - Term 2</p>
            <p>Uganda O-Level Education System</p>
          </div>
        </div>

        <div class="bg-secondary-50 rounded-lg p-4 border border-secondary-200">
          <h5 class="font-semibold text-secondary-900 mb-2">Features</h5>
          <ul class="text-sm text-secondary-800 space-y-1">
            <li>• Complete Student Management</li>
            <li>• Uganda-Compliant Assessment System</li>
            <li>• Advanced Reporting & Analytics</li>
            <li>• Role-Based Access Control</li>
            <li>• Real-time Data Synchronization</li>
          </ul>
        </div>
      </div>
    </div>
  `, [
    { text: 'Close', onclick: 'closeModal()', class: 'bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-xl font-medium transition-colors duration-200' }
  ], { size: 'lg' });
}

// Export functions to global scope
window.togglePassword = togglePassword;
window.showModal = showModal;
window.closeModal = closeModal;
window.showConfirm = showConfirm;
window.showSkeleton = showSkeletonLoader;
window.showLoadingButton = showLoadingButton;
window.hideLoadingButton = hideLoadingButton;
window.initializeGlobalSearch = initializeGlobalSearch;
window.clearGlobalSearch = clearGlobalSearch;
// Keyboard shortcuts removed
window.initializeTooltips = initializeTooltips;
window.showQuickAdd = showQuickAdd;
window.showHelp = showHelp;
window.showAbout = showAbout;
window.loadDashboard = loadDashboard;
