const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// =============================================
// CONTINUOUS ASSESSMENTS ROUTES
// =============================================

// Get O-Level continuous assessments with filters
router.get('/continuous/o-level', async (req, res) => {
  try {
    const { student_id, subject_id, term_id, academic_year_id, teacher_id } = req.query;

    let query = `
      SELECT
        ca.*,
        s.admission_number, s.first_name, s.last_name,
        sub.name as subject_name, sub.short_name as subject_short_name,
        ay.name as academic_year_name,
        term.name as term_name,
        teach.first_name as teacher_first_name, teach.last_name as teacher_last_name
      FROM o_level_continuous_assessments ca
      JOIN students s ON ca.student_id = s.id
      JOIN o_level_subjects sub ON ca.subject_id = sub.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      JOIN teachers teach ON ca.teacher_id = teach.id
      WHERE 1=1
    `;
    
    let params = [];

    if (student_id) {
      query += ' AND ca.student_id = ?';
      params.push(student_id);
    }

    if (subject_id) {
      query += ' AND ca.subject_id = ?';
      params.push(subject_id);
    }

    if (term_id) {
      query += ' AND ca.term_id = ?';
      params.push(term_id);
    }

    if (academic_year_id) {
      query += ' AND ca.academic_year_id = ?';
      params.push(academic_year_id);
    }

    if (teacher_id) {
      query += ' AND ca.teacher_id = ?';
      params.push(teacher_id);
    }

    query += ' ORDER BY ca.assessment_date DESC, s.admission_number';
    
    const result = await executeQuery(query, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get O-Level continuous assessments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve O-Level continuous assessments'
    });
  }
});

// Get A-Level Principal continuous assessments with filters
router.get('/continuous/a-level', async (req, res) => {
  try {
    const { student_id, subject_id, term_id, academic_year_id, teacher_id } = req.query;

    let query = `
      SELECT
        ca.*,
        s.admission_number, s.first_name, s.last_name,
        sub.name as subject_name, sub.short_name as subject_short_name,
        ay.name as academic_year_name,
        term.name as term_name,
        teach.first_name as teacher_first_name, teach.last_name as teacher_last_name
      FROM a_level_principal_continuous_assessments ca
      JOIN students s ON ca.student_id = s.id
      JOIN a_level_subjects sub ON ca.subject_id = sub.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      JOIN teachers teach ON ca.teacher_id = teach.id
      WHERE 1=1
    `;

    let params = [];

    if (student_id) {
      query += ' AND ca.student_id = ?';
      params.push(student_id);
    }

    if (subject_id) {
      query += ' AND ca.subject_id = ?';
      params.push(subject_id);
    }

    if (term_id) {
      query += ' AND ca.term_id = ?';
      params.push(term_id);
    }

    if (academic_year_id) {
      query += ' AND ca.academic_year_id = ?';
      params.push(academic_year_id);
    }

    if (teacher_id) {
      query += ' AND ca.teacher_id = ?';
      params.push(teacher_id);
    }

    query += ' ORDER BY ca.assessment_date DESC, s.admission_number';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get A-Level continuous assessments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level continuous assessments'
    });
  }
});

// Get O-Level continuous assessment by ID
router.get('/continuous/o-level/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        ca.*,
        s.admission_number, s.first_name, s.last_name,
        sub.name as subject_name, sub.short_name as subject_short_name,
        ay.name as academic_year_name,
        term.name as term_name,
        teach.first_name as teacher_first_name, teach.last_name as teacher_last_name
      FROM o_level_continuous_assessments ca
      JOIN students s ON ca.student_id = s.id
      JOIN o_level_subjects sub ON ca.subject_id = sub.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      JOIN teachers teach ON ca.teacher_id = teach.id
      WHERE ca.id = ?
    `;
    
    const result = await executeQuery(query, [id]);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Continuous assessment not found'
      });
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get O-Level continuous assessment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve O-Level continuous assessment'
    });
  }
});

// Get A-Level Principal continuous assessment by ID
router.get('/continuous/a-level/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        ca.*,
        s.admission_number, s.first_name, s.last_name,
        sub.name as subject_name, sub.short_name as subject_short_name,
        ay.name as academic_year_name,
        term.name as term_name,
        teach.first_name as teacher_first_name, teach.last_name as teacher_last_name
      FROM a_level_principal_continuous_assessments ca
      JOIN students s ON ca.student_id = s.id
      JOIN a_level_subjects sub ON ca.subject_id = sub.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      JOIN teachers teach ON ca.teacher_id = teach.id
      WHERE ca.id = ?
    `;

    const result = await executeQuery(query, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'A-Level continuous assessment not found'
      });
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get A-Level continuous assessment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level continuous assessment'
    });
  }
});

// Create O-Level continuous assessment
router.post('/continuous/o-level', async (req, res) => {
  try {
    const {
      student_id, subject_id, academic_year_id, term_id,
      competency_score, competency_remark, assessment_date, assessment_method, teacher_id,
      general_skills, general_remarks
    } = req.body;

    // Validate required fields
    if (!student_id || !subject_id || !academic_year_id || !term_id ||
        competency_score === undefined || !assessment_date || !assessment_method || !teacher_id) {
      return res.status(400).json({
        success: false,
        message: 'All required fields must be provided'
      });
    }

    // Validate competency score (1-3 scale)
    if (competency_score < 1 || competency_score > 3) {
      return res.status(400).json({
        success: false,
        message: 'Competency score must be between 1 and 3'
      });
    }

    // Get current user ID for audit fields
    const userId = req.user?.id || 1; // Default to system admin if no user context

    const insertQuery = `
      INSERT INTO o_level_continuous_assessments (
        student_id, subject_id, academic_year_id, term_id,
        competency_score, competency_remark, assessment_date, assessment_method, teacher_id,
        general_skills, general_remarks, created_by_id, updated_by_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [
      student_id, subject_id, academic_year_id, term_id,
      competency_score, competency_remark, assessment_date, assessment_method, teacher_id,
      general_skills, general_remarks, userId
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get the created assessment with related data
    const newAssessmentQuery = `
      SELECT
        ca.*,
        s.admission_number, s.first_name, s.last_name,
        sub.name as subject_name,
        ay.name as academic_year_name,
        term.name as term_name
      FROM o_level_continuous_assessments ca
      JOIN students s ON ca.student_id = s.id
      JOIN o_level_subjects sub ON ca.subject_id = sub.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      WHERE ca.id = ?
    `;

    const newAssessmentResult = await executeQuery(newAssessmentQuery, [result.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'O-Level continuous assessment created successfully',
      data: newAssessmentResult.data[0]
    });

  } catch (error) {
    console.error('Create O-Level continuous assessment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create O-Level continuous assessment'
    });
  }
});

// Create A-Level Principal continuous assessment
router.post('/continuous/a-level', async (req, res) => {
  try {
    const {
      student_id, subject_id, academic_year_id, term_id,
      competency_score, competency_remark, assessment_date, assessment_method, teacher_id,
      general_skills, general_remarks
    } = req.body;

    // Validate required fields
    if (!student_id || !subject_id || !academic_year_id || !term_id ||
        competency_score === undefined || !assessment_date || !assessment_method || !teacher_id) {
      return res.status(400).json({
        success: false,
        message: 'All required fields must be provided'
      });
    }

    // Validate competency score (1-3 scale)
    if (competency_score < 1 || competency_score > 3) {
      return res.status(400).json({
        success: false,
        message: 'Competency score must be between 1 and 3'
      });
    }

    // Get current user ID for audit fields
    const userId = req.user?.id || 1; // Default to system admin if no user context

    const insertQuery = `
      INSERT INTO a_level_principal_continuous_assessments (
        student_id, subject_id, academic_year_id, term_id,
        competency_score, competency_remark, assessment_date, assessment_method, teacher_id,
        general_skills, general_remarks, created_by_id, updated_by_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [
      student_id, subject_id, academic_year_id, term_id,
      competency_score, competency_remark, assessment_date, assessment_method, teacher_id,
      general_skills, general_remarks, userId
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get the created assessment with related data
    const newAssessmentQuery = `
      SELECT
        ca.*,
        s.admission_number, s.first_name, s.last_name,
        sub.name as subject_name,
        ay.name as academic_year_name,
        term.name as term_name
      FROM a_level_principal_continuous_assessments ca
      JOIN students s ON ca.student_id = s.id
      JOIN a_level_subjects sub ON ca.subject_id = sub.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      WHERE ca.id = ?
    `;

    const newAssessmentResult = await executeQuery(newAssessmentQuery, [result.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'A-Level continuous assessment created successfully',
      data: newAssessmentResult.data[0]
    });

  } catch (error) {
    console.error('Create A-Level continuous assessment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create A-Level continuous assessment'
    });
  }
});

// Update continuous assessment
router.put('/continuous/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      student_id, subject_id, topic_id, academic_year_id, term_id,
      competency_score, assessment_date, assessment_method, teacher_id,
      comments, skills_demonstrated, evidence_notes
    } = req.body;

    // Check if assessment exists
    const checkQuery = 'SELECT id FROM continuous_assessments WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);
    
    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Continuous assessment not found'
      });
    }

    // Validate competency score if provided
    if (competency_score !== undefined && (competency_score < 0 || competency_score > 3)) {
      return res.status(400).json({
        success: false,
        message: 'Competency score must be between 0 and 3'
      });
    }

    const updateQuery = `
      UPDATE continuous_assessments SET
        student_id = ?, subject_id = ?, topic_id = ?, academic_year_id = ?, term_id = ?,
        competency_score = ?, assessment_date = ?, assessment_method = ?, teacher_id = ?,
        comments = ?, skills_demonstrated = ?, evidence_notes = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const result = await executeQuery(updateQuery, [
      student_id, subject_id, topic_id, academic_year_id, term_id,
      competency_score, assessment_date, assessment_method, teacher_id,
      comments, skills_demonstrated, evidence_notes, id
    ]);

    if (!result.success) {
      throw new Error(result.error);
    }

    // Get updated assessment
    const updatedAssessmentQuery = `
      SELECT 
        ca.*,
        s.admission_number, s.first_name, s.last_name,
        sub.name as subject_name,
        t.name as topic_name,
        ay.name as academic_year_name,
        term.name as term_name
      FROM continuous_assessments ca
      JOIN students s ON ca.student_id = s.id
      JOIN subjects sub ON ca.subject_id = sub.id
      JOIN topics t ON ca.topic_id = t.id
      JOIN academic_years ay ON ca.academic_year_id = ay.id
      JOIN terms term ON ca.term_id = term.id
      WHERE ca.id = ?
    `;

    const updatedAssessmentResult = await executeQuery(updatedAssessmentQuery, [id]);

    res.json({
      success: true,
      message: 'Continuous assessment updated successfully',
      data: updatedAssessmentResult.data[0]
    });

  } catch (error) {
    console.error('Update continuous assessment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update continuous assessment'
    });
  }
});

// Delete continuous assessment
router.delete('/continuous/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if assessment exists
    const checkQuery = 'SELECT id FROM continuous_assessments WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Continuous assessment not found'
      });
    }

    // Delete assessment
    const deleteQuery = 'DELETE FROM continuous_assessments WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'Continuous assessment deleted successfully'
    });

  } catch (error) {
    console.error('Delete continuous assessment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete continuous assessment'
    });
  }
});

// =============================================
// EXAMINATIONS ROUTES
// =============================================

// Get O-Level examinations with filters
router.get('/examinations/o-level', async (req, res) => {
  try {
    const { class_id, subject_id, teacher_id, term_id, academic_year_id } = req.query;

    let query = `
      SELECT
        e.*,
        c.name as class_name,
        sub.name as subject_name, sub.short_name as subject_short_name,
        ay.name as academic_year_name,
        term.name as term_name,
        teach.first_name as teacher_first_name, teach.last_name as teacher_last_name
      FROM o_level_term_examinations e
      JOIN classes c ON e.class_id = c.id
      JOIN o_level_subjects sub ON e.subject_id = sub.id
      JOIN academic_years ay ON e.academic_year_id = ay.id
      JOIN terms term ON e.term_id = term.id
      JOIN teachers teach ON e.teacher_id = teach.id
      WHERE 1=1
    `;

    let params = [];

    if (class_id) {
      query += ' AND e.class_id = ?';
      params.push(class_id);
    }

    if (subject_id) {
      query += ' AND e.subject_id = ?';
      params.push(subject_id);
    }

    if (teacher_id) {
      query += ' AND e.teacher_id = ?';
      params.push(teacher_id);
    }

    if (term_id) {
      query += ' AND e.term_id = ?';
      params.push(term_id);
    }

    if (academic_year_id) {
      query += ' AND e.academic_year_id = ?';
      params.push(academic_year_id);
    }

    query += ' ORDER BY e.created_at DESC, c.name, sub.name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get O-Level examinations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve O-Level examinations'
    });
  }
});

// Get A-Level examinations with filters
router.get('/examinations/a-level', async (req, res) => {
  try {
    const { class_id, subject_id, teacher_id, term_id, academic_year_id } = req.query;

    let query = `
      SELECT
        e.*,
        c.name as class_name,
        sub.name as subject_name, sub.short_name as subject_short_name,
        ay.name as academic_year_name,
        term.name as term_name,
        teach.first_name as teacher_first_name, teach.last_name as teacher_last_name
      FROM a_level_principal_term_examinations e
      JOIN classes c ON e.class_id = c.id
      JOIN a_level_subjects sub ON e.subject_id = sub.id
      JOIN academic_years ay ON e.academic_year_id = ay.id
      JOIN terms term ON e.term_id = term.id
      JOIN teachers teach ON e.teacher_id = teach.id
      WHERE 1=1
    `;

    let params = [];

    if (class_id) {
      query += ' AND e.class_id = ?';
      params.push(class_id);
    }

    if (subject_id) {
      query += ' AND e.subject_id = ?';
      params.push(subject_id);
    }

    if (teacher_id) {
      query += ' AND e.teacher_id = ?';
      params.push(teacher_id);
    }

    if (term_id) {
      query += ' AND e.term_id = ?';
      params.push(term_id);
    }

    if (academic_year_id) {
      query += ' AND e.academic_year_id = ?';
      params.push(academic_year_id);
    }

    query += ' ORDER BY e.created_at DESC, c.name, sub.name';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get A-Level examinations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level examinations'
    });
  }
});

// =============================================
// ASSESSMENT STATISTICS
// =============================================

// Get assessment statistics
router.get('/stats/overview', async (req, res) => {
  try {
    const { academic_year_id, term_id } = req.query;

    let whereClause = 'WHERE 1=1';
    let params = [];

    if (academic_year_id) {
      whereClause += ' AND academic_year_id = ?';
      params.push(academic_year_id);
    }

    if (term_id) {
      whereClause += ' AND term_id = ?';
      params.push(term_id);
    }

    // Get O-Level statistics
    const oLevelStatsQuery = `
      SELECT
        COUNT(*) as total_assessments,
        COUNT(CASE WHEN competency_score >= 2.5 THEN 1 END) as excellent_performance,
        COUNT(CASE WHEN competency_score >= 2.0 AND competency_score < 2.5 THEN 1 END) as good_performance,
        COUNT(CASE WHEN competency_score >= 1.5 AND competency_score < 2.0 THEN 1 END) as satisfactory_performance,
        COUNT(CASE WHEN competency_score < 1.5 THEN 1 END) as needs_improvement,
        AVG(competency_score) as average_score,
        COUNT(DISTINCT student_id) as students_assessed,
        COUNT(DISTINCT subject_id) as subjects_assessed,
        'O-Level' as level_type
      FROM o_level_continuous_assessments
      ${whereClause}
    `;

    // Get A-Level statistics
    const aLevelStatsQuery = `
      SELECT
        COUNT(*) as total_assessments,
        COUNT(CASE WHEN competency_score >= 2.5 THEN 1 END) as excellent_performance,
        COUNT(CASE WHEN competency_score >= 2.0 AND competency_score < 2.5 THEN 1 END) as good_performance,
        COUNT(CASE WHEN competency_score >= 1.5 AND competency_score < 2.0 THEN 1 END) as satisfactory_performance,
        COUNT(CASE WHEN competency_score < 1.5 THEN 1 END) as needs_improvement,
        AVG(competency_score) as average_score,
        COUNT(DISTINCT student_id) as students_assessed,
        COUNT(DISTINCT subject_id) as subjects_assessed,
        'A-Level' as education_level_name
      FROM a_level_principal_continuous_assessments
      ${whereClause}
    `;

    // Execute both queries
    const [oLevelResult, aLevelResult] = await Promise.all([
      executeQuery(oLevelStatsQuery, params),
      executeQuery(aLevelStatsQuery, params)
    ]);

    // Combine results
    const combinedStats = {
      o_level: oLevelResult.success ? oLevelResult.data[0] : null,
      a_level: aLevelResult.success ? aLevelResult.data[0] : null,
      total_assessments: (oLevelResult.success ? oLevelResult.data[0].total_assessments : 0) +
                        (aLevelResult.success ? aLevelResult.data[0].total_assessments : 0),
      total_students_assessed: new Set([
        ...(oLevelResult.success ? [oLevelResult.data[0].students_assessed] : []),
        ...(aLevelResult.success ? [aLevelResult.data[0].students_assessed] : [])
      ]).size
    };

    res.json({
      success: true,
      data: combinedStats
    });

  } catch (error) {
    console.error('Get assessment stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve assessment statistics'
    });
  }
});

module.exports = router;
