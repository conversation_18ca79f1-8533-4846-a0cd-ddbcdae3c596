/**
 * Validation Middleware
 * Provides middleware functions for automatic validation of requests
 */

const { BusinessValidation } = require('../utils/validation');

/**
 * Middleware to validate teacher data
 */
const validateTeacher = (isUpdate = false) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id || 1;
      const teacherData = {
        ...req.body,
        [isUpdate ? 'updated_by_id' : 'created_by_id']: userId
      };

      const validationErrors = await BusinessValidation.validateTeacher(teacherData, isUpdate, req.params.id);
      const auditErrors = BusinessValidation.validateAuditFields(teacherData, isUpdate);
      const allErrors = [...validationErrors, ...auditErrors];

      if (allErrors.length > 0) {
        return res.status(400).json(BusinessValidation.formatValidationErrors(allErrors));
      }

      // Add validated data to request for use in route handler
      req.validatedData = teacherData;
      next();
    } catch (error) {
      console.error('Teacher validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Middleware to validate student data
 */
const validateStudent = (isUpdate = false) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id || 1;
      const studentData = {
        ...req.body,
        [isUpdate ? 'updated_by_id' : 'created_by_id']: userId
      };

      const validationErrors = await BusinessValidation.validateStudent(studentData, isUpdate, req.params.id);
      const auditErrors = BusinessValidation.validateAuditFields(studentData, isUpdate);
      const allErrors = [...validationErrors, ...auditErrors];

      if (allErrors.length > 0) {
        return res.status(400).json(BusinessValidation.formatValidationErrors(allErrors));
      }

      req.validatedData = studentData;
      next();
    } catch (error) {
      console.error('Student validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Middleware to validate academic year data
 */
const validateAcademicYear = (isUpdate = false) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id || 1;
      const academicYearData = {
        ...req.body,
        [isUpdate ? 'updated_by_id' : 'created_by_id']: userId
      };

      const validationErrors = await BusinessValidation.validateAcademicYear(academicYearData, isUpdate, req.params.id);
      const auditErrors = BusinessValidation.validateAuditFields(academicYearData, isUpdate);
      const allErrors = [...validationErrors, ...auditErrors];

      if (allErrors.length > 0) {
        return res.status(400).json(BusinessValidation.formatValidationErrors(allErrors));
      }

      req.validatedData = academicYearData;
      next();
    } catch (error) {
      console.error('Academic year validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Middleware to validate term data
 */
const validateTerm = (isUpdate = false) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id || 1;
      const termData = {
        ...req.body,
        [isUpdate ? 'updated_by_id' : 'created_by_id']: userId
      };

      const validationErrors = await BusinessValidation.validateTerm(termData, isUpdate, req.params.id);
      const auditErrors = BusinessValidation.validateAuditFields(termData, isUpdate);
      const allErrors = [...validationErrors, ...auditErrors];

      if (allErrors.length > 0) {
        return res.status(400).json(BusinessValidation.formatValidationErrors(allErrors));
      }

      req.validatedData = termData;
      next();
    } catch (error) {
      console.error('Term validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Middleware to validate competency scores
 */
const validateCompetencyScore = () => {
  return (req, res, next) => {
    try {
      const { competency_score } = req.body;

      if (competency_score !== undefined) {
        const errors = BusinessValidation.validateCompetencyScore(competency_score);
        if (errors.length > 0) {
          return res.status(400).json(BusinessValidation.formatValidationErrors(errors));
        }
      }

      next();
    } catch (error) {
      console.error('Competency score validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Middleware to validate exam marks and percentages
 */
const validateExamMarks = () => {
  return (req, res, next) => {
    try {
      const errors = BusinessValidation.validateExamMarks(req.body);
      if (errors.length > 0) {
        return res.status(400).json(BusinessValidation.formatValidationErrors(errors));
      }

      next();
    } catch (error) {
      console.error('Exam marks validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Middleware to validate subject selection
 */
const validateSubjectSelection = () => {
  return async (req, res, next) => {
    try {
      const errors = await BusinessValidation.validateSubjectSelection(req.body);
      if (errors.length > 0) {
        return res.status(400).json(BusinessValidation.formatValidationErrors(errors));
      }

      next();
    } catch (error) {
      console.error('Subject selection validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

/**
 * Generic validation middleware that can be configured for different entity types
 */
const validateEntity = (entityType, isUpdate = false) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id || 1;
      const entityData = {
        ...req.body,
        [isUpdate ? 'updated_by_id' : 'created_by_id']: userId
      };

      let validationErrors = [];
      
      switch (entityType) {
        case 'teacher':
          validationErrors = await BusinessValidation.validateTeacher(entityData, isUpdate, req.params.id);
          break;
        case 'student':
          validationErrors = await BusinessValidation.validateStudent(entityData, isUpdate, req.params.id);
          break;
        case 'academicYear':
          validationErrors = await BusinessValidation.validateAcademicYear(entityData, isUpdate, req.params.id);
          break;
        case 'term':
          validationErrors = await BusinessValidation.validateTerm(entityData, isUpdate, req.params.id);
          break;
        default:
          throw new Error(`Unknown entity type: ${entityType}`);
      }

      const auditErrors = BusinessValidation.validateAuditFields(entityData, isUpdate);
      const allErrors = [...validationErrors, ...auditErrors];

      if (allErrors.length > 0) {
        return res.status(400).json(BusinessValidation.formatValidationErrors(allErrors));
      }

      req.validatedData = entityData;
      next();
    } catch (error) {
      console.error(`${entityType} validation middleware error:`, error);
      res.status(500).json({
        success: false,
        message: 'Validation error occurred'
      });
    }
  };
};

module.exports = {
  validateTeacher,
  validateStudent,
  validateAcademicYear,
  validateTerm,
  validateCompetencyScore,
  validateExamMarks,
  validateSubjectSelection,
  validateEntity
};
