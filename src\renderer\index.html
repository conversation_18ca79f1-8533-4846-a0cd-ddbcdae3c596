<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIMS - Report Card Generation System</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554'
                        },
                        secondary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                            950: '#082f49'
                        },
                        accent: {
                            50: '#ecfdf5',
                            100: '#d1fae5',
                            200: '#a7f3d0',
                            300: '#6ee7b7',
                            400: '#34d399',
                            500: '#10b981',
                            600: '#059669',
                            700: '#047857',
                            800: '#065f46',
                            900: '#064e3b',
                            950: '#022c22'
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d'
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f'
                        },
                        danger: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#ef4444',
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d'
                        },
                        info: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e'
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                        display: ['Inter', 'system-ui', 'sans-serif'],
                    },
                    fontSize: {
                        'xs': ['0.75rem', { lineHeight: '1rem' }],
                        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
                        'base': ['1rem', { lineHeight: '1.5rem' }],
                        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
                        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
                        '2xl': ['1.5rem', { lineHeight: '2rem' }],
                        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
                        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
                        '5xl': ['3rem', { lineHeight: '1' }],
                        '6xl': ['3.75rem', { lineHeight: '1' }],
                        '7xl': ['4.5rem', { lineHeight: '1' }],
                        '8xl': ['6rem', { lineHeight: '1' }],
                        '9xl': ['8rem', { lineHeight: '1' }],
                    },
                    spacing: {
                        '18': '4.5rem',
                        '88': '22rem',
                        '128': '32rem',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'fade-out': 'fadeOut 0.3s ease-in-out',
                        'slide-in-left': 'slideInLeft 0.5s ease-out',
                        'slide-in-right': 'slideInRight 0.5s ease-out',
                        'slide-in-up': 'slideInUp 0.4s ease-out',
                        'slide-in-down': 'slideInDown 0.4s ease-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'bounce-in': 'bounceIn 0.6s ease-out',
                        'pulse-slow': 'pulse 3s infinite',
                        'spin-slow': 'spin 3s linear infinite',
                        'wiggle': 'wiggle 1s ease-in-out infinite',
                        'float': 'float 3s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'shimmer': 'shimmer 2s linear infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        fadeOut: {
                            '0%': { opacity: '1' },
                            '100%': { opacity: '0' }
                        },
                        slideInLeft: {
                            '0%': { transform: 'translateX(-100%)', opacity: '0' },
                            '100%': { transform: 'translateX(0)', opacity: '1' }
                        },
                        slideInRight: {
                            '0%': { transform: 'translateX(100%)', opacity: '0' },
                            '100%': { transform: 'translateX(0)', opacity: '1' }
                        },
                        slideInUp: {
                            '0%': { transform: 'translateY(100%)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        slideInDown: {
                            '0%': { transform: 'translateY(-100%)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        bounceIn: {
                            '0%': { transform: 'scale(0.3)', opacity: '0' },
                            '50%': { transform: 'scale(1.05)' },
                            '70%': { transform: 'scale(0.9)' },
                            '100%': { transform: 'scale(1)', opacity: '1' }
                        },
                        wiggle: {
                            '0%, 100%': { transform: 'rotate(-3deg)' },
                            '50%': { transform: 'rotate(3deg)' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 5px rgba(59, 130, 246, 0.5)' },
                            '100%': { boxShadow: '0 0 20px rgba(59, 130, 246, 0.8)' }
                        },
                        shimmer: {
                            '0%': { backgroundPosition: '-200% 0' },
                            '100%': { backgroundPosition: '200% 0' }
                        }
                    },
                    backdropBlur: {
                        xs: '2px',
                    },
                    boxShadow: {
                        'soft': '0 2px 15px 0 rgba(0, 0, 0, 0.08)',
                        'medium': '0 4px 25px 0 rgba(0, 0, 0, 0.1)',
                        'hard': '0 10px 40px 0 rgba(0, 0, 0, 0.15)',
                        'glow': '0 0 20px rgba(59, 130, 246, 0.3)',
                        'glow-lg': '0 0 40px rgba(59, 130, 246, 0.4)',
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/logo.png">

    <!--  Component Styles -->
    <style>
        /*  component styles for new features */
        .search-filter {
            padding: 0.5rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 0.5rem;
            transition: all 0.2s;
            border: 1px solid #E5E7EB;
            color: #6B7280;
            background: white;
            cursor: pointer;
        }

        .search-filter:hover {
            background: #F9FAFB;
        }

        .search-filter.active {
            background: #3B82F6;
            color: white;
            border-color: #3B82F6;
        }

        .export-type-btn, .export-format-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 0.75rem;
            border: 2px solid #E5E7EB;
            transition: all 0.2s;
            color: #6B7280;
            background: white;
            cursor: pointer;
        }

        .export-type-btn:hover, .export-format-btn:hover {
            border-color: #93C5FD;
            background: #EFF6FF;
        }

        .export-type-btn.active, .export-format-btn.active {
            background: #3B82F6;
            color: white;
            border-color: #3B82F6;
        }

        .form-input, .form-select, .form-textarea {
            display: block;
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #D1D5DB;
            border-radius: 0.75rem;
            transition: all 0.2s;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            box-shadow: 0 0 0 2px #3B82F6;
            border-color: #3B82F6;
        }

        .form-checkbox {
            width: 1rem;
            height: 1rem;
            color: #2563EB;
            border-color: #D1D5DB;
            border-radius: 0.25rem;
        }

        .form-checkbox:focus {
            box-shadow: 0 0 0 2px #3B82F6;
        }

        .btn-primary {
            background: #2563EB;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 500;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            border: none;
            cursor: pointer;
        }

        .btn-primary:hover {
            background: #1D4ED8;
        }

        .btn-secondary {
            background: #F3F4F6;
            color: #374151;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 500;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
        }

        .btn-secondary:hover {
            background: #E5E7EB;
        }

        .kbd {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: #6B7280;
            background: #F3F4F6;
            border: 1px solid #D1D5DB;
            border-radius: 0.25rem;
        }

        /*  Sidebar Navigation Styles */
        .sidebar-menu-section {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-menu-section.collapsed {
            max-height: 0;
            opacity: 0;
            margin-top: 0;
            margin-bottom: 0;
        }

        .sidebar-menu-section.expanded {
            max-height: 500px;
            opacity: 1;
        }

        .sidebar-menu-button {
            position: relative;
            overflow: hidden;
        }

        .sidebar-menu-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .sidebar-menu-button:hover::before {
            left: 100%;
        }

        .sidebar-chevron {
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-chevron.rotated {
            transform: rotate(180deg);
        }

        /* Active menu item indicator */
        .menu-item-active {
            position: relative;
        }

        .menu-item-active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 60%;
            background: linear-gradient(to bottom, #3B82F6, #1D4ED8);
            border-radius: 0 2px 2px 0;
        }

        /* Submenu animation */
        .submenu-enter {
            animation: submenuSlideIn 0.3s ease-out forwards;
        }

        .submenu-exit {
            animation: submenuSlideOut 0.3s ease-out forwards;
        }

        @keyframes submenuSlideIn {
            from {
                max-height: 0;
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                max-height: 300px;
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes submenuSlideOut {
            from {
                max-height: 300px;
                opacity: 1;
                transform: translateY(0);
            }
            to {
                max-height: 0;
                opacity: 0;
                transform: translateY(-10px);
            }
        }

        /* Hover effects for menu items */
        .menu-item:hover {
            transform: translateX(2px);
        }

        .submenu-item:hover {
            transform: translateX(4px);
        }

        /* Focus styles for accessibility */
        .sidebar-menu-button:focus,
        .menu-item:focus,
        .submenu-item:focus {
            outline: 2px solid #3B82F6;
            outline-offset: 2px;
        }

        /* Collapsible Sidebar Styles - Optimized for Desktop */
        .sidebar {
            transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            width: 280px; /* Reduced from 320px for better desktop space usage */
            overflow-x: hidden;
            min-width: 0;
            max-width: 280px;
        }

        .sidebar.collapsed {
            width: 70px; /* Reduced from 80px for cleaner look */
            overflow-x: hidden;
        }

        /* Desktop-optimized responsive breakpoints */
        @media (max-width: 1200px) {
            .sidebar {
                width: 260px;
            }
            .sidebar.collapsed {
                width: 65px;
            }
        }

        @media (max-width: 1024px) {
            .sidebar {
                width: 240px;
            }
            .sidebar.collapsed {
                width: 60px;
            }
        }

        .sidebar-text {
            transition: opacity 0.3s ease, transform 0.3s ease;
            opacity: 1;
            transform: translateX(0);
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }

        .sidebar.collapsed .sidebar-text {
            opacity: 0;
            transform: translateX(-10px);
            pointer-events: none;
        }

        .sidebar-brand-text {
            transition: opacity 0.3s ease, transform 0.3s ease;
            opacity: 1;
            transform: translateX(0);
            overflow: hidden;
            min-width: 0;
            flex-shrink: 1;
        }

        .sidebar.collapsed .sidebar-brand-text {
            opacity: 0;
            transform: translateX(-10px);
        }

        .sidebar-toggle-btn {
            transition: transform 0.3s ease;
        }

        .sidebar.collapsed .sidebar-toggle-btn {
            transform: rotate(180deg);
        }

        /* Menu item styles for collapsed state */
        .sidebar.collapsed .menu-item {
            justify-content: center;
            padding: 0.75rem;
        }

        .sidebar.collapsed .submenu-item {
            display: none;
        }

        .sidebar.collapsed .sidebar-menu-section {
            display: none;
        }

        /* Tooltip for collapsed sidebar */
        .sidebar-tooltip {
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background: #1F2937;
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 1000;
            margin-left: 0.5rem;
            pointer-events: none;
        }

        .sidebar-tooltip::before {
            content: '';
            position: absolute;
            right: 100%;
            top: 50%;
            transform: translateY(-50%);
            border: 5px solid transparent;
            border-right-color: #1F2937;
        }

        .sidebar.collapsed .menu-item:hover .sidebar-tooltip {
            opacity: 1;
            visibility: visible;
        }

        /* Active menu item highlighting */
        .menu-item.active {
            background: linear-gradient(135deg, #3B82F6, #1D4ED8);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .menu-item.active .menu-icon {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .submenu-item.active {
            background: linear-gradient(135deg, #3B82F6, #1D4ED8);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
        }

        .submenu-item.active .submenu-dot {
            background: white;
        }

        /* Main content adjustment */
        .main-content {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-left: 0;
        }

        .main-content.sidebar-collapsed {
            margin-left: 0;
        }







        /*  animations for sidebar toggle */
        @keyframes sidebarExpand {
            from {
                width: 80px;
            }
            to {
                width: 320px;
            }
        }

        @keyframes sidebarCollapse {
            from {
                width: 320px;
            }
            to {
                width: 80px;
            }
        }

        /* Active menu item glow effect */
        .menu-item.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(29, 78, 216, 0.1));
            border-radius: 0.75rem;
            z-index: -1;
        }

        /* Submenu active item glow effect */
        .submenu-item.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(29, 78, 216, 0.1));
            border-radius: 0.5rem;
            z-index: -1;
        }

        /* Sidebar header logo animation */
        .sidebar.collapsed .sidebar-brand-text {
            transform: scale(0) translateX(-20px);
        }

        /* Menu item icon scaling in collapsed state */
        .sidebar.collapsed .menu-icon {
            transform: scale(1.1);
        }

        /* Smooth transitions for all sidebar elements */
        .sidebar * {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Prevent text selection during animations */
        .sidebar.collapsed * {
            user-select: none;
        }

        /* Enhanced Modal Backdrop Styles */
        .modal-backdrop {
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            background: rgba(0, 0, 0, 0.5);
            animation: backdropFadeIn 0.3s ease-out forwards;
        }

        .modal-backdrop.closing {
            animation: backdropFadeOut 0.2s ease-out forwards;
        }

        @keyframes backdropFadeIn {
            from {
                opacity: 0;
                backdrop-filter: blur(0px);
                -webkit-backdrop-filter: blur(0px);
            }
            to {
                opacity: 1;
                backdrop-filter: blur(8px);
                -webkit-backdrop-filter: blur(8px);
            }
        }

        @keyframes backdropFadeOut {
            from {
                opacity: 1;
                backdrop-filter: blur(8px);
                -webkit-backdrop-filter: blur(8px);
            }
            to {
                opacity: 0;
                backdrop-filter: blur(0px);
                -webkit-backdrop-filter: blur(0px);
            }
        }

        /* Enhanced Modal Content Animation */
        .animate-modal-in {
            animation: modalSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
        }

        .animate-modal-out {
            animation: modalSlideOut 0.2s ease-out forwards;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes modalSlideOut {
            from {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            to {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
        }

        /* Prevent horizontal scrolling */
        body, html {
            overflow-x: hidden;
        }

        /* Ensure main content doesn't overflow */
        .main-content {
            overflow-x: hidden;
            max-width: 100vw;
        }

        /* Sidebar content overflow prevention */
        .sidebar .flex-1 {
            overflow-x: hidden;
            min-width: 0;
        }

        .sidebar nav {
            overflow-x: hidden;
            min-width: 0;
        }

        .sidebar ul {
            overflow-x: hidden;
            min-width: 0;
        }

        .sidebar li {
            overflow-x: hidden;
            min-width: 0;
        }

        /* Menu item text handling */
        .sidebar .menu-item,
        .sidebar .submenu-item {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            min-width: 0;
        }

        /* Sidebar toggle button tooltip */
        .sidebar-toggle-tooltip {
            position: absolute;
            right: -120px;
            top: 50%;
            transform: translateY(-50%);
            background: #1F2937;
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 1000;
            pointer-events: none;
        }

        .sidebar-toggle-tooltip::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 50%;
            transform: translateY(-50%);
            border: 5px solid transparent;
            border-right-color: #1F2937;
        }

        .sidebar-toggle-btn:hover .sidebar-toggle-tooltip {
            opacity: 1;
            visibility: visible;
        }

        /* Enhanced toggle button animation */
        .sidebar-toggle-btn {
            position: relative;
        }

        .sidebar-toggle-btn:hover {
            transform: scale(1.05);
        }

        .sidebar.collapsed .sidebar-toggle-btn i {
            transform: rotate(180deg);
        }





        /* Desktop-optimized responsive design */

        /* Large Desktop (1400px+) */
        @media (min-width: 1400px) {
            .sidebar {
                width: 300px;
            }
            .sidebar.collapsed {
                width: 75px;
            }
            .content-body {
                padding: 2rem;
            }
        }

        /* Standard Desktop (1200px - 1399px) */
        @media (min-width: 1200px) and (max-width: 1399px) {
            .sidebar {
                width: 280px;
            }
            .sidebar.collapsed {
                width: 70px;
            }
            .content-body {
                padding: 1.5rem;
            }
        }

        /* Small Desktop (1024px - 1199px) */
        @media (min-width: 1024px) and (max-width: 1199px) {
            .sidebar {
                width: 260px;
            }
            .sidebar.collapsed {
                width: 65px;
            }
            .content-body {
                padding: 1.25rem;
            }
        }

        /* Tablet/Small Desktop (768px - 1023px) */
        @media (min-width: 768px) and (max-width: 1023px) {
            .sidebar {
                width: 240px;
            }
            .sidebar.collapsed {
                width: 60px;
            }
            .content-body {
                padding: 1rem;
            }
        }

        /* Mobile (below 768px) - Minimal responsive for desktop app */
        @media (max-width: 767px) {
            .sidebar {
                width: 220px;
            }
            .sidebar.collapsed {
                width: 55px;
            }
            .content-body {
                padding: 0.75rem;
            }
        }

        /* Desktop layout optimization */
        .desktop-layout {
            display: flex;
            height: 100vh;
            overflow: hidden;
            background: #f9fafb;
        }

        .content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            min-width: 0;
        }

        .content-body {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            background: #f9fafb;
            transition: padding 0.3s ease;
        }

        /* Smooth transitions for all layout changes */
        .sidebar, .main-content, .content-wrapper {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Modern Layout Styles */
        .nav-section-items.collapsed {
            max-height: 0;
            opacity: 0;
        }

        .nav-section-items.expanded {
            max-height: 500px;
            opacity: 1;
        }

        /* Sidebar Collapsed State Styles */
        #sidebar.sidebar-collapsed {
            overflow-x: hidden;
        }

        #sidebar.sidebar-collapsed .nav-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
            white-space: nowrap;
            transition: all 0.3s ease;
        }

        #sidebar.sidebar-collapsed .nav-item,
        #sidebar.sidebar-collapsed .nav-section {
            position: relative;
        }

        #sidebar.sidebar-collapsed .nav-item:hover::after,
        #sidebar.sidebar-collapsed .nav-section:hover::after {
            content: attr(title);
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background: #1f2937;
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            white-space: nowrap;
            z-index: 1000;
            margin-left: 0.5rem;
            opacity: 1;
            visibility: visible;
            transition: all 0.2s ease;
            pointer-events: none;
        }

        #sidebar.sidebar-collapsed .nav-item:hover::before,
        #sidebar.sidebar-collapsed .nav-section:hover::before {
            content: '';
            position: absolute;
            left: calc(100% + 0.25rem);
            top: 50%;
            transform: translateY(-50%);
            border: 5px solid transparent;
            border-right-color: #1f2937;
            z-index: 1001;
        }

        /* Ensure icons are centered when collapsed */
        #sidebar.sidebar-collapsed .nav-item button,
        #sidebar.sidebar-collapsed .nav-section button {
            justify-content: center;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }

        /* Hide chevron icons when collapsed */
        #sidebar.sidebar-collapsed .fa-chevron-down {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        /* Smooth hover effects */
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Loading states */
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
    </style>
</head>
<body>
    <!--  Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-gradient-to-br from-primary-600 via-secondary-600 to-accent-600 flex items-center justify-center z-50 overflow-hidden">
        <!-- Animated Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-shimmer" style="background-size: 200% 100%;"></div>
            <div class="absolute top-0 left-0 w-full h-full">
                <div class="absolute top-1/4 left-1/4 w-32 h-32 bg-white/5 rounded-full animate-float" style="animation-delay: 0s;"></div>
                <div class="absolute top-3/4 right-1/4 w-24 h-24 bg-white/5 rounded-full animate-float" style="animation-delay: 1s;"></div>
                <div class="absolute bottom-1/4 left-1/3 w-16 h-16 bg-white/5 rounded-full animate-float" style="animation-delay: 2s;"></div>
            </div>
        </div>

        <div class="relative z-10 text-center text-white animate-fade-in max-w-md mx-auto px-6">
            <!--  Logo Container -->
            <div class="mb-8 animate-bounce-in">
                <div class="relative w-32 h-32 mx-auto mb-6">
                    <!-- Outer Ring -->
                    <div class="absolute inset-0 bg-gradient-to-r from-white/20 to-white/10 rounded-full animate-spin-slow"></div>
                    <!-- Inner Circle -->
                    <div class="absolute inset-2 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border-2 border-white/30 shadow-glow">
                        <img src="../assets/images/logo.png" alt="AIMS Logo" class="w-20 h-20 object-contain hidden animate-glow" id="logo-image">
                        <div class="text-4xl font-bold text-white animate-glow" id="logo-placeholder">AIMS</div>
                    </div>
                    <!-- Pulse Ring -->
                    <div class="absolute inset-0 border-2 border-white/20 rounded-full animate-ping"></div>
                </div>
            </div>

            <!--  Loading Text -->
            <div class="mb-8 animate-slide-up">
                <h1 class="text-3xl font-bold mb-3 tracking-wide bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                    Report Card System
                </h1>
                <p id="loading-message" class="text-xl opacity-90 font-medium mb-2">Initializing system...</p>
            </div>

            <!-- Advanced Loading Indicator -->
            <div class="mb-8">
                <!-- Progress Bar -->
                <div class="w-64 h-2 bg-white/20 rounded-full mx-auto mb-4 overflow-hidden">
                    <div class="h-full bg-gradient-to-r from-white/60 to-white/80 rounded-full animate-shimmer" style="width: 0%; animation-duration: 3s; animation-iteration-count: infinite;"></div>
                </div>

                <!-- Spinner -->
                <div class="flex justify-center">
                    <div class="relative">
                        <div class="w-16 h-16 border-4 border-white/20 border-t-white/80 rounded-full animate-spin"></div>
                        <div class="absolute inset-2 border-2 border-transparent border-r-white/40 rounded-full animate-spin" style="animation-direction: reverse; animation-duration: 2s;"></div>
                        <div class="absolute inset-4 w-4 h-4 bg-white/60 rounded-full animate-pulse"></div>
                    </div>
                </div>
            </div>

            <!--  Progress Dots -->
            <div class="flex justify-center space-x-3">
                <div class="flex space-x-1">
                    <div class="w-3 h-3 bg-white/60 rounded-full animate-pulse" style="animation-delay: 0s;"></div>
                    <div class="w-3 h-3 bg-white/60 rounded-full animate-pulse" style="animation-delay: 0.3s;"></div>
                    <div class="w-3 h-3 bg-white/60 rounded-full animate-pulse" style="animation-delay: 0.6s;"></div>
                </div>
            </div>

            <!-- Loading Status -->
            <div class="mt-6 text-sm opacity-70">
                <div id="loading-status" class="animate-fade-in">Preparing your workspace...</div>
            </div>
        </div>
    </div>

    <!--  Login Screen -->
    <div id="login-screen" class="fixed inset-0 flex z-40" style="display: none;">
        <!-- Left Panel -  Branding -->
        <div class="flex-1 bg-gradient-to-br from-primary-600 via-secondary-600 to-accent-600 flex flex-col items-center justify-center p-8 text-white relative overflow-hidden animate-slide-in-left">
            <!--  Background Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-shimmer" style="background-size: 200% 100%;"></div>
                <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 100 100&quot;><defs><pattern id=&quot;grid&quot; width=&quot;10&quot; height=&quot;10&quot; patternUnits=&quot;userSpaceOnUse&quot;><path d=&quot;M 10 0 L 0 0 0 10&quot; fill=&quot;none&quot; stroke=&quot;rgba(255,255,255,0.1)&quot; stroke-width=&quot;0.5&quot;/></pattern></defs><rect width=&quot;100&quot; height=&quot;100&quot; fill=&quot;url(%23grid)&quot;/></svg>');"></div>
                <!-- Floating Elements -->
                <div class="absolute top-1/4 left-1/4 w-32 h-32 bg-white/5 rounded-full animate-float" style="animation-delay: 0s;"></div>
                <div class="absolute top-3/4 right-1/4 w-24 h-24 bg-white/5 rounded-full animate-float" style="animation-delay: 1s;"></div>
                <div class="absolute bottom-1/4 left-1/3 w-16 h-16 bg-white/5 rounded-full animate-float" style="animation-delay: 2s;"></div>
            </div>

            <div class="relative z-10 text-center max-w-lg animate-fade-in">
                <!--  Logo -->
                <div class="mb-8">
                    <div class="relative w-32 h-32 mx-auto mb-6">
                        <!-- Outer Ring -->
                        <div class="absolute inset-0 bg-gradient-to-r from-white/20 to-white/10 rounded-full animate-spin-slow"></div>
                        <!-- Inner Circle -->
                        <div class="absolute inset-2 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border-2 border-white/30 shadow-glow">
                            <img src="../assets/images/logo.png" alt="AIMS Logo" class="w-24 h-24 object-contain hidden animate-glow" onload="this.classList.remove('hidden'); this.nextElementSibling && this.nextElementSibling.classList.add('hidden');" onerror="this.classList.add('hidden'); this.nextElementSibling && this.nextElementSibling.classList.remove('hidden');">
                        </div>
                        <!-- Pulse Ring -->
                        <div class="absolute inset-0 border-2 border-white/20 rounded-full animate-ping"></div>
                    </div>
                </div>

                <!--  Title and Subtitle -->
                <h1 class="text-4xl font-bold mb-4 leading-tight bg-gradient-to-r from-white to-white/90 bg-clip-text text-transparent">
                    RCGS
                </h1>
                <p class="text-xl mb-8 opacity-90 font-light">Built for Secondary Schools</p>

                <!--  Features List -->
                <div class="grid grid-cols-2 gap-4 mb-8">
                    <div class="flex items-center space-x-3 animate-slide-up" style="animation-delay: 0.1s;">
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-file-alt text-lg"></i>
                        </div>
                        <span class="font-medium text-left">Report Card Generation</span>
                    </div>
                    <div class="flex items-center space-x-3 animate-slide-up" style="animation-delay: 0.2s;">
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-chart-bar text-lg"></i>
                        </div>
                        <span class="font-medium text-left">Performance Analytics</span>
                    </div>
                    <div class="flex items-center space-x-3 animate-slide-up" style="animation-delay: 0.3s;">
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-graduation-cap text-lg"></i>
                        </div>
                        <span class="font-medium text-left">Secondary Schools</span>
                    </div>
                    <div class="flex items-center space-x-3 animate-slide-up" style="animation-delay: 0.4s;">
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-shield-alt text-lg"></i>
                        </div>
                        <span class="font-medium text-left">Secure Platform</span>
                    </div>
                </div>
            </div>

        </div>

        <!-- Right Panel -  Login Form -->
        <div class="flex-1 bg-gray-50 flex items-center justify-center p-8 animate-slide-in-right">
            <div class="w-full max-w-md">
                <!--  Login Header -->
                <div class="text-center mb-8">
                    <h1 class="text-3xl font-bold text-primary-900 mb-2">LOGIN</h1>
                    <p class="text-gray-600">Enter your credentials to access the system</p>
                </div>

                <!--  Login Form -->
                <div class="bg-white rounded-2xl shadow-soft border border-gray-100 p-8">
                    <form id="login-form" class="space-y-6">
                        <!-- Username Field -->
                        <div class="animate-slide-up" style="animation-delay: 0.1s;">
                            <label for="username" class="block text-sm font-semibold text-gray-700 mb-3">Username</label>
                            <div class="relative group">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <i class="fas fa-user text-gray-400 group-focus-within:text-primary-500 transition-colors duration-200"></i>
                                </div>
                                <input type="text" id="username" name="username" required
                                       class="w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-xl focus:border-primary-500 focus:ring-4 focus:ring-primary-100 transition-all duration-200 bg-gray-50 focus:bg-white text-gray-900 placeholder-gray-500 text-lg"
                                       placeholder="Enter your username"
                                       autocomplete="username">
                            </div>
                        </div>

                        <!-- Password Field -->
                        <div class="animate-slide-up" style="animation-delay: 0.2s;">
                            <label for="password" class="block text-sm font-semibold text-gray-700 mb-3">Password</label>
                            <div class="relative group">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-gray-400 group-focus-within:text-primary-500 transition-colors duration-200"></i>
                                </div>
                                <input type="password" id="password" name="password" required
                                       class="w-full pl-12 pr-14 py-4 border-2 border-gray-200 rounded-xl focus:border-primary-500 focus:ring-4 focus:ring-primary-100 transition-all duration-200 bg-gray-50 focus:bg-white text-gray-900 placeholder-gray-500 text-lg"
                                       placeholder="Enter your password"
                                       autocomplete="current-password">
                                <button type="button" class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-primary-600 transition-colors duration-200" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="password-toggle-icon"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Remember Me & Forgot Password -->
                        <div class="flex items-center justify-between animate-slide-up" style="animation-delay: 0.25s;">
                            <label class="flex items-center">
                                <input type="checkbox" class="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 focus:ring-2">
                                <span class="ml-2 text-sm text-gray-600">Remember me</span>
                            </label>
                            <a href="#" onclick="showForgotPassword()" class="text-sm text-primary-600 hover:text-primary-800 font-medium transition-colors duration-200 hover:underline">
                                Forgot Password?
                            </a>
                        </div>

                        <!--  Login Button -->
                        <div class="animate-slide-up" style="animation-delay: 0.3s;">
                            <button type="submit" class="w-full bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-[1.02] hover:shadow-medium focus:outline-none focus:ring-4 focus:ring-primary-200 flex items-center justify-center space-x-3 group shadow-soft">
                                <i class="fas fa-sign-in-alt group-hover:translate-x-1 transition-transform duration-200"></i>
                                <span class="text-lg">Sign In</span>
                            </button>
                        </div>

                        <!-- Login Help -->
                        <div class="text-center animate-slide-up" style="animation-delay: 0.4s;">
                            <p class="text-sm text-gray-500 mb-2">Need help accessing your account?</p>
                            <button type="button" onclick="showLoginHelp()" class="text-sm text-primary-600 hover:text-primary-800 font-medium transition-colors duration-200 hover:underline">
                                Contact Administrator
                            </button>
                        </div>
                    </form>
                </div>


            </div>
        </div>
    </div>

    <!--  Main Application -->
    <div id="main-app" style="display: none;">
        <!-- Layout will be injected here by Layout.init() -->
    </div>






    <!--  Modal Container -->
    <div id="modal-container" class="fixed inset-0 z-50 hidden">
        <!-- Modal backdrop with blur effect -->
        <div class="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 z-40 bg-white/80 backdrop-blur-sm hidden">
        <div class="flex items-center justify-center h-full">
            <div class="text-center">
                <div class="w-16 h-16 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
                <p class="text-gray-600 font-medium">Loading...</p>
            </div>
        </div>
    </div>


    <!-- JavaScript Files -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>


    <!-- Environment Configuration (loaded first) -->
    <script src="../assets/js/config/environment.js"></script>

    <!-- API Services (loaded second) -->
    <script src="../assets/js/services/api.js"></script>

    <!-- Layout System (loaded third) -->
    <script src="../assets/js/layout.js"></script>
    <script src="../assets/js/navigation.js"></script>
    <script src="../assets/js/dashboard.js"></script>
    <script src="../assets/js/page-router.js"></script>

    <!-- Core Application Scripts -->
    <script src="../assets/js/design-system.js"></script>
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/ui-helpers.js"></script>

    <!-- Utilities -->
    <script src="../assets/js/utils/audit-fields.js"></script>
    <script src="../assets/js/utils/audit-fields-test.js"></script>

    <!-- Authentication -->
    <script src="../assets/js/auth.js"></script>

    <!-- Components -->
    <script src="../assets/js/components/academic-years.js"></script>
    <script src="../assets/js/components/assessment-management.js"></script>
    <script src="../assets/js/components/classes-streams.js"></script>
    <script src="../assets/js/components/grade-boundaries.js"></script>
    <script src="../assets/js/components/school-settings.js"></script>
    <script src="../assets/js/components/student-management.js"></script>
    <script src="../assets/js/components/subjects-management.js"></script>
    <script src="../assets/js/components/teacher-management.js"></script>

    <!-- Main Application -->
    <script src="../assets/js/main.js"></script>

    <!-- Global Sidebar Toggle Function -->
    <script>
        // Global sidebar toggle function
        function toggleSidebar() {
            if (window.Layout && window.Layout.toggleSidebar) {
                window.Layout.toggleSidebar();
            } else {
                // Fallback implementation
                console.log('Using fallback toggleSidebar function');
                const sidebar = document.getElementById('sidebar');
                if (sidebar) {
                    const isCollapsed = sidebar.classList.contains('collapsed');
                    if (isCollapsed) {
                        sidebar.classList.remove('collapsed');
                        localStorage.setItem('sidebar-collapsed', 'false');
                        console.log('Sidebar expanded (fallback)');
                    } else {
                        sidebar.classList.add('collapsed');
                        localStorage.setItem('sidebar-collapsed', 'true');
                        console.log('Sidebar collapsed (fallback)');
                    }

                    // Trigger layout recalculation
                    setTimeout(() => {
                        window.dispatchEvent(new Event('resize'));
                    }, 300);
                } else {
                    console.error('Sidebar element not found');
                }
            }
        }

        // Global functions for backward compatibility
        function logout() {
            if (window.AuthManager) {
                window.AuthManager.logout();
            }
        }

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('password-toggle-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        function showForgotPassword() {
            alert('Please contact your system administrator for password reset.');
        }

        function showLoginHelp() {
            alert('For technical support, please contact your system administrator.');
        }

        // Note: Main application initialization is handled by main.js
        // which has its own DOMContentLoaded event listener
    </script>

    <!-- Scripts already loaded above -->
</body>
</html>
