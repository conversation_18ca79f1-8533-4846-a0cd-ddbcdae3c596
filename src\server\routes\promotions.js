const express = require('express');
const { executeQuery, executeTransaction } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get promotion criteria and eligible students
router.get('/eligible-students', async (req, res) => {
  try {
    const { from_class_id, academic_year_id, term_id } = req.query;

    if (!from_class_id || !academic_year_id || !term_id) {
      return res.status(400).json({
        success: false,
        message: 'From class ID, academic year ID, and term ID are required'
      });
    }

    // Get students in the specified class
    const studentsQuery = `
      SELECT 
        s.id, s.admission_number, s.first_name, s.middle_name, s.last_name,
        s.gender, s.status,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        COUNT(CASE WHEN ca.competency_score >= 2.0 THEN 1 END) as passing_assessments,
        COUNT(DISTINCT ca.subject_id) as subjects_assessed
      FROM students s
      JOIN student_enrollments se ON s.id = se.student_id
      LEFT JOIN continuous_assessments ca ON s.id = ca.student_id 
        AND ca.academic_year_id = se.academic_year_id 
        AND ca.term_id = se.term_id
      WHERE se.class_id = ? AND se.academic_year_id = ? AND se.term_id = ?
        AND s.status = 'active'
      GROUP BY s.id, s.admission_number, s.first_name, s.middle_name, s.last_name, s.gender, s.status
      ORDER BY s.admission_number
    `;
    
    const result = await executeQuery(studentsQuery, [from_class_id, academic_year_id, term_id]);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    // Calculate promotion eligibility for each student
    const eligibleStudents = result.data.map(student => {
      const passingPercentage = student.total_assessments > 0 
        ? (student.passing_assessments / student.total_assessments) * 100 
        : 0;
      
      // Basic promotion criteria: average score >= 2.0 and at least 60% passing assessments
      const isEligible = student.average_score >= 2.0 && passingPercentage >= 60;
      
      return {
        ...student,
        passing_percentage: Math.round(passingPercentage * 100) / 100,
        is_eligible: isEligible,
        promotion_status: isEligible ? 'eligible' : 'needs_review'
      };
    });

    res.json({
      success: true,
      data: eligibleStudents
    });

  } catch (error) {
    console.error('Get eligible students error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve eligible students'
    });
  }
});

// Get available target classes for promotion
router.get('/target-classes', async (req, res) => {
  try {
    const { from_class_id, academic_year_id } = req.query;

    if (!from_class_id || !academic_year_id) {
      return res.status(400).json({
        success: false,
        message: 'From class ID and academic year ID are required'
      });
    }

    // Get the current class level
    const currentClassQuery = `
      SELECT c.class_level_id, cl.sort_order, el.code as education_level_code
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      WHERE c.id = ?
    `;
    
    const currentClassResult = await executeQuery(currentClassQuery, [from_class_id]);
    
    if (!currentClassResult.success || currentClassResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Source class not found'
      });
    }

    const currentClass = currentClassResult.data[0];

    // Get target classes (next level up)
    const targetClassesQuery = `
      SELECT
        c.id, c.name, c.current_enrollment,
        cl.name as class_level_name, cl.sort_order,
        el.code as education_level_code, el.name as education_level_name,
        s.name as stream_name
      FROM classes c
      JOIN class_levels cl ON c.class_level_id = cl.id
      JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams s ON c.stream_id = s.id
      WHERE cl.sort_order > ?
        AND c.is_active = TRUE
      ORDER BY cl.sort_order, c.name
    `;
    
    const result = await executeQuery(targetClassesQuery, [currentClass.sort_order]);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: {
        current_class: currentClass,
        target_classes: result.data
      }
    });

  } catch (error) {
    console.error('Get target classes error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve target classes'
    });
  }
});

// Promote students
router.post('/promote', async (req, res) => {
  try {
    const {
      student_promotions,
      academic_year_id,
      term_id,
      promoted_by_id
    } = req.body;

    // Validate required fields
    if (!student_promotions || !Array.isArray(student_promotions) || student_promotions.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Student promotions array is required'
      });
    }

    if (!academic_year_id || !term_id || !promoted_by_id) {
      return res.status(400).json({
        success: false,
        message: 'Academic year ID, term ID, and promoted by ID are required'
      });
    }

    // Execute promotions in a transaction
    const promotionResults = await executeTransaction(async (connection) => {
      const results = [];

      for (const promotion of student_promotions) {
        const { student_id, from_class_id, to_class_id, promotion_type, status, remarks } = promotion;

        // Validate promotion data
        if (!student_id || !from_class_id || !to_class_id) {
          throw new Error(`Invalid promotion data for student ${student_id}`);
        }

        // Record the promotion
        const promotionQuery = `
          INSERT INTO student_promotions (
            student_id, from_class_id, to_class_id, academic_year_id,
            promotion_type, status, remarks, promoted_by_id, promotion_date,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW())
        `;

        const promotionResult = await connection.execute(promotionQuery, [
          student_id, from_class_id, to_class_id, academic_year_id,
          promotion_type || 'regular', status || 'completed', remarks, promoted_by_id
        ]);

        // Create new enrollment for the promoted student
        const enrollmentQuery = `
          INSERT INTO student_enrollments (
            student_id, class_id, academic_year_id, term_id, enrollment_date, enrolled_by_id, created_at, updated_at
          ) VALUES (?, ?, ?, ?, NOW(), ?, NOW(), NOW())
        `;

        await connection.execute(enrollmentQuery, [
          student_id, to_class_id, academic_year_id, term_id, promoted_by_id
        ]);

        // Update class enrollment counts
        await connection.execute(
          'UPDATE classes SET current_enrollment = current_enrollment - 1 WHERE id = ?',
          [from_class_id]
        );

        await connection.execute(
          'UPDATE classes SET current_enrollment = current_enrollment + 1 WHERE id = ?',
          [to_class_id]
        );

        results.push({
          student_id,
          promotion_id: promotionResult[0].insertId,
          status: 'success'
        });
      }

      return results;
    });

    res.json({
      success: true,
      message: 'Students promoted successfully',
      data: {
        promoted_count: promotionResults.length,
        promotions: promotionResults
      }
    });

  } catch (error) {
    console.error('Promote students error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to promote students: ' + error.message
    });
  }
});

// Get promotion history
router.get('/history', async (req, res) => {
  try {
    const { student_id, academic_year_id, class_id } = req.query;
    
    let query = `
      SELECT
        sp.*,
        s.admission_number, s.first_name, s.last_name,
        fc.name as from_class_name,
        tc.name as to_class_name,
        ay.name as academic_year,
        su.first_name as promoted_by_first_name,
        su.last_name as promoted_by_last_name
      FROM student_promotions sp
      JOIN students s ON sp.student_id = s.id
      JOIN classes fc ON sp.from_class_id = fc.id
      JOIN classes tc ON sp.to_class_id = tc.id
      JOIN academic_years ay ON sp.academic_year_id = ay.id
      LEFT JOIN system_users su ON sp.promoted_by_id = su.id
      WHERE 1=1
    `;
    
    let params = [];
    
    if (student_id) {
      query += ' AND sp.student_id = ?';
      params.push(student_id);
    }
    
    if (academic_year_id) {
      query += ' AND sp.academic_year_id = ?';
      params.push(academic_year_id);
    }
    
    if (class_id) {
      query += ' AND (sp.from_class_id = ? OR sp.to_class_id = ?)';
      params.push(class_id, class_id);
    }
    
    query += ' ORDER BY sp.promotion_date DESC, s.admission_number';
    
    const result = await executeQuery(query, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get promotion history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve promotion history'
    });
  }
});

// Get promotion statistics
router.get('/stats', async (req, res) => {
  try {
    const { academic_year_id } = req.query;
    
    let whereClause = 'WHERE 1=1';
    let params = [];

    if (academic_year_id) {
      whereClause += ' AND academic_year_id = ?';
      params.push(academic_year_id);
    }

    const statsQuery = `
      SELECT
        COUNT(*) as total_promotions,
        COUNT(CASE WHEN promotion_type = 'regular' THEN 1 END) as regular_promotions,
        COUNT(CASE WHEN promotion_type = 'conditional' THEN 1 END) as conditional_promotions,
        COUNT(CASE WHEN promotion_type = 'repeat' THEN 1 END) as repeated_students,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_promotions,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_promotions,
        COUNT(DISTINCT student_id) as unique_students_promoted,
        COUNT(DISTINCT from_class_id) as classes_involved
      FROM student_promotions
      ${whereClause}
    `;
    
    const result = await executeQuery(statsQuery, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get promotion stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve promotion statistics'
    });
  }
});

module.exports = router;
