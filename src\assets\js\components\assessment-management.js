// AIMS Assessment Management Components
// Comprehensive assessment management system for CAs and Exams

// Uses global API services: window.AssessmentsAPI, window.SubjectsAPI, etc.
// Uses global config: window.AIMSConfig
// Uses environment configuration: ../config/environment.js

const AssessmentManagementComponents = {
  // Component state
  state: {
    assessments: [],
    subjects: [],
    classes: [],
    teachers: [],
    academicYears: [],
    terms: [],
    loading: false,
    filters: {
      academicYear: '',
      term: '',
      subject: '',
      class: '',
      assessmentType: ''
    }
  },

  // Initialize component
  async init() {
    await this.loadInitialData();
  },

  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;

      console.log('🔄 Loading assessment management data...');

      // Use the API services with proper error handling
      const [assessments, oLevelSubjects, aLevelSubjects, classes, teachers, academicYears, terms] = await Promise.all([
        window.AssessmentsAPI.getAll(),
        window.SubjectsAPI.oLevel.getAll(),
        window.SubjectsAPI.aLevel.getAll(),
        window.ClassesAPI.getAll(),
        window.TeachersAPI.getAll(),
        window.AcademicYearsAPI.getAll(),
        window.TermsAPI.getAll()
      ]);

      // Combine subjects with level indicators
      const allSubjects = [
        ...(oLevelSubjects.data || []).map(s => ({ ...s, level: 'o_level' })),
        ...(aLevelSubjects.data || []).map(s => ({ ...s, level: 'a_level' }))
      ];

      this.state.assessments = assessments;
      this.state.subjects = { success: true, data: allSubjects };
      this.state.classes = classes;
      this.state.teachers = teachers;
      this.state.academicYears = academicYears;
      this.state.terms = terms;

      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.log('✅ Assessment data loaded:', { assessments, oLevelSubjects, aLevelSubjects, classes, teachers, academicYears, terms });
      }

    } catch (error) {
      console.error('❌ Failed to load initial data:', error);
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.error('Debug: Assessment data loading error details:', error);
      }
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to load assessment data', 'error');
      }
    } finally {
      this.state.loading = false;
    }
  },

  // Get current admin ID from authentication
  getCurrentAdminId() {
    try {
      // Try to get from localStorage first
      const adminData = localStorage.getItem('aims_admin_data');
      if (adminData) {
        const parsed = JSON.parse(adminData);
        return parsed.id || parsed.user_id;
      }

      // Try to get from JWT token
      const token = localStorage.getItem('aims_token');
      if (token) {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return payload.id || payload.user_id || payload.userId;
      }

      // Fallback: try to get from global auth state
      if (window.AuthManager && window.AuthManager.getCurrentUser) {
        const user = window.AuthManager.getCurrentUser();
        return user?.id;
      }

      return null;
    } catch (error) {
      console.error('Error getting current admin ID:', error);
      return null;
    }
  }
};



// Enter CA Scores Component
const EnterCAScoresComponent = {
  // Component state
  state: {
    selectedAssessment: null,
    students: [],
    scores: {}
  },

  // Render enter CA scores interface
  render() {
    return `
      <div class="space-y-6">
        ${AIMSDesignSystem.layouts.pageHeader(
          'Enter CA Scores',
          'Input continuous assessment scores for students'
        )}

        <!-- Assessment Selection -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Select Assessment</h3>
            ${AIMSDesignSystem.forms.button('create-new-ca', 'Create New CA', 'secondary', {
              onclick: 'EnterCAScoresComponent.showCreateCAModal()'
            })}
          </div>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            ${AIMSDesignSystem.forms.select('ca_academic_year', 'Academic Year', [], '')}
            ${AIMSDesignSystem.forms.select('ca_term', 'Term', [], '')}
            ${AIMSDesignSystem.forms.select('ca_subject', 'Subject', [], '')}
            ${AIMSDesignSystem.forms.select('ca_assessment', 'Assessment', [], '')}
          </div>
          <div class="mt-4">
            ${AIMSDesignSystem.forms.button('load-students', 'Load Students', 'primary', {
              onclick: 'EnterCAScoresComponent.loadStudents()'
            })}
          </div>
        </div>

        <!-- Assessment Details -->
        <div id="assessment-details" class="hidden bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <!-- Assessment details will be populated here -->
        </div>

        <!-- Scores Entry -->
        <div id="scores-entry-section" class="hidden bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900">Enter Scores</h3>
              <div class="flex items-center space-x-4">
                ${AIMSDesignSystem.forms.button('save-scores', 'Save All Scores', 'primary', {
                  onclick: 'EnterCAScoresComponent.saveScores()'
                })}
                ${AIMSDesignSystem.forms.button('auto-fill', 'Auto Fill', 'secondary', {
                  onclick: 'EnterCAScoresComponent.showAutoFillModal()'
                })}
              </div>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admission No.</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Comments</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody id="scores-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Students and scores will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Auto Fill Modal -->
      <div id="auto-fill-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-xl bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
              <h3 class="text-lg font-semibold text-gray-900">Auto Fill Scores</h3>
              <button onclick="EnterCAScoresComponent.closeAutoFillModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
              </button>
            </div>
            <div class="mt-6 space-y-4">
              ${AIMSDesignSystem.forms.input('auto_fill_score', 'Score to Fill', '', {
                type: 'number',
                min: '1',
                max: '3',
                step: '0.1',
                placeholder: 'Enter score (1-3 scale)'
              })}
              <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
                ${AIMSDesignSystem.forms.button('cancel-auto-fill', 'Cancel', 'secondary', {
                  onclick: 'EnterCAScoresComponent.closeAutoFillModal()'
                })}
                ${AIMSDesignSystem.forms.button('apply-auto-fill', 'Apply to All', 'primary', {
                  onclick: 'EnterCAScoresComponent.applyAutoFill()'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Create New CA Modal -->
      <div id="create-ca-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-10 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-xl bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
              <h3 class="text-lg font-semibold text-gray-900">Create New CA Assessment</h3>
              <button onclick="EnterCAScoresComponent.closeCreateCAModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
              </button>
            </div>
            <form id="create-ca-form" class="mt-6 space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                ${AIMSDesignSystem.forms.input('ca_title', 'Assessment Title', '', {
                  required: true,
                  placeholder: 'e.g., Mathematics Topic Assessment 1'
                })}
                ${AIMSDesignSystem.forms.select('ca_method', 'CA Assessment Method', [
                  { value: '', label: 'Select CA Method' },
                  { value: 'topic_assessment', label: 'Topic-based Assessment' },
                  { value: 'activity_integration', label: 'Activities of Integration' },
                  { value: 'project', label: 'Project' },
                  { value: 'assignment', label: 'Assignment' },
                  { value: 'group_work', label: 'Group Work' },
                  { value: 'practical_exercise', label: 'Practical Exercise' }
                ], '', { required: true })}
              </div>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                ${AIMSDesignSystem.forms.select('ca_new_academic_year', 'Academic Year', [], '', { required: true })}
                ${AIMSDesignSystem.forms.select('ca_new_term', 'Term', [], '', { required: true })}
                ${AIMSDesignSystem.forms.select('ca_new_subject', 'Subject', [], '', { required: true })}
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                ${AIMSDesignSystem.forms.select('ca_new_class', 'Class', [], '', { required: true })}
                ${AIMSDesignSystem.forms.select('ca_new_teacher', 'Teacher', [], '', { required: true })}
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                ${AIMSDesignSystem.forms.input('ca_assessment_date', 'Assessment Date', '', {
                  type: 'date',
                  required: true
                })}
                ${AIMSDesignSystem.forms.input('ca_due_date', 'Due Date', '', {
                  type: 'date',
                  placeholder: 'Optional'
                })}
              </div>
              <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
                ${AIMSDesignSystem.forms.button('cancel-create-ca', 'Cancel', 'secondary', {
                  type: 'button',
                  onclick: 'EnterCAScoresComponent.closeCreateCAModal()'
                })}
                ${AIMSDesignSystem.forms.button('save-create-ca', 'Create CA Assessment', 'primary', {
                  type: 'submit'
                })}
              </div>
            </form>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize component
  async init() {
    await AssessmentManagementComponents.loadInitialData();
    this.populateDropdowns();
    this.initializeEventListeners();
  },

  // Get current admin ID from authentication
  getCurrentAdminId() {
    return AssessmentManagementComponents.getCurrentAdminId();
  },

  // Populate dropdowns
  populateDropdowns() {
    // Similar to ManageAssessmentsComponent but filtered for CA assessments
    const academicYearSelect = document.getElementById('ca_academic_year');
    if (academicYearSelect) {
      academicYearSelect.innerHTML = '<option value="">Select Academic Year</option>';
      const academicYears = AssessmentManagementComponents.state.academicYears.data || AssessmentManagementComponents.state.academicYears;
      if (Array.isArray(academicYears)) {
        academicYears.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
          if (year.is_active) option.selected = true;
          academicYearSelect.appendChild(option);
        });
      }
    }

    // Populate other dropdowns similarly...
  },

  // Load students for selected assessment
  async loadStudents() {
    const assessmentId = document.getElementById('ca_assessment').value;
    if (!assessmentId) {
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Please select an assessment', 'error');
      }
      return;
    }

    try {
      // Use the API service
      const result = await window.AssessmentsAPI.getStudents(assessmentId);

      if (result.success) {
        this.state.students = result.data.students;
        this.state.selectedAssessment = result.data.assessment;
        this.renderAssessmentDetails();
        this.renderScoresTable();
        document.getElementById('assessment-details').classList.remove('hidden');
        document.getElementById('scores-entry-section').classList.remove('hidden');
      } else {
        // Show error notification if available
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show(result.message || 'Failed to load students', 'error');
        }
      }
    } catch (error) {
      console.error('Load students error:', error);
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to load students', 'error');
      }
    }
  },

  // Render assessment details
  renderAssessmentDetails() {
    const container = document.getElementById('assessment-details');
    if (!container || !this.state.selectedAssessment) return;

    const assessment = this.state.selectedAssessment;
    container.innerHTML = `
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700">Assessment</label>
          <p class="mt-1 text-sm text-gray-900">${assessment.assessment_name}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Subject</label>
          <p class="mt-1 text-sm text-gray-900">${assessment.subject_name}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Class</label>
          <p class="mt-1 text-sm text-gray-900">${assessment.class_name}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Maximum Score</label>
          <p class="mt-1 text-sm text-gray-900">${assessment.max_score}</p>
        </div>
      </div>
    `;
  },

  // Render scores table
  renderScoresTable() {
    const tbody = document.getElementById('scores-table-body');
    if (!tbody) return;

    tbody.innerHTML = this.state.students.map(student => `
      <tr>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <img class="h-8 w-8 rounded-full object-cover"
                 src="${student.passport_photo || '/assets/images/default-avatar.png'}"
                 alt="${student.first_name} ${student.last_name}">
            <div class="ml-3">
              <div class="text-sm font-medium text-gray-900">${student.first_name} ${student.last_name}</div>
            </div>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${student.admission_number}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <input type="number"
                 id="score_${student.id}"
                 min="0"
                 max="${this.state.selectedAssessment.max_score}"
                 step="0.1"
                 value="${student.current_score || ''}"
                 class="w-20 px-3 py-2 border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                 onchange="EnterCAScoresComponent.updateScore(${student.id}, this.value)">
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <input type="text"
                 id="comment_${student.id}"
                 value="${student.current_comment || ''}"
                 placeholder="Optional comment..."
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                 onchange="EnterCAScoresComponent.updateComment(${student.id}, this.value)">
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <span id="status_${student.id}" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            student.current_score !== null ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          }">
            ${student.current_score !== null ? 'Entered' : 'Pending'}
          </span>
        </td>
      </tr>
    `).join('');
  },

  // Update score for student
  updateScore(studentId, score) {
    if (!this.state.scores[studentId]) {
      this.state.scores[studentId] = {};
    }
    this.state.scores[studentId].score = score ? parseFloat(score) : null;

    // Update status indicator
    const statusElement = document.getElementById(`status_${studentId}`);
    if (statusElement) {
      if (score) {
        statusElement.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800';
        statusElement.textContent = 'Entered';
      } else {
        statusElement.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800';
        statusElement.textContent = 'Pending';
      }
    }
  },

  // Update comment for student
  updateComment(studentId, comment) {
    if (!this.state.scores[studentId]) {
      this.state.scores[studentId] = {};
    }
    this.state.scores[studentId].comment = comment;
  },

  // Show auto fill modal
  showAutoFillModal() {
    document.getElementById('auto-fill-modal').classList.remove('hidden');
  },

  // Close auto fill modal
  closeAutoFillModal() {
    document.getElementById('auto-fill-modal').classList.add('hidden');
    document.getElementById('auto_fill_score').value = '';
  },

  // Apply auto fill
  applyAutoFill() {
    const score = document.getElementById('auto_fill_score').value;
    if (!score) {
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Please enter a score', 'error');
      }
      return;
    }

    this.state.students.forEach(student => {
      const scoreInput = document.getElementById(`score_${student.id}`);
      if (scoreInput) {
        scoreInput.value = score;
        this.updateScore(student.id, score);
      }
    });

    this.closeAutoFillModal();
    // Show success notification if available
    if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
      window.AIMSDesignSystem.notifications.show('Scores applied to all students', 'success');
    }
  },

  // Save scores
  async saveScores() {
    if (!this.state.selectedAssessment) {
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('No assessment selected', 'error');
      }
      return;
    }

    // Get current admin ID for tracking
    const currentAdminId = this.getCurrentAdminId();

    const scoresData = Object.entries(this.state.scores).map(([studentId, data]) => ({
      student_id: parseInt(studentId),
      assessment_id: this.state.selectedAssessment.id,
      score: data.score,
      comment: data.comment || null,
      created_by_id: currentAdminId, // Include system admin tracking
      updated_by_id: currentAdminId
    })).filter(item => item.score !== null);

    if (scoresData.length === 0) {
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('No scores to save', 'error');
      }
      return;
    }

    try {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-scores', true);
      }

      // Use the API service
      const result = await window.CAScoresAPI.create(scoresData);

      if (result.success) {
        // Show success notification if available
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show(`${scoresData.length} scores saved successfully!`, 'success');
        }
        this.state.scores = {};
      } else {
        // Show error notification if available
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show(result.message || 'Failed to save scores', 'error');
        }
      }
    } catch (error) {
      console.error('Save scores error:', error);
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.error('Debug: Save scores error details:', error);
      }
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to save scores', 'error');
      }
    } finally {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-scores', false);
      }
    }
  },

  // Show Create CA Modal
  showCreateCAModal() {
    const modal = document.getElementById('create-ca-modal');
    if (modal) {
      modal.classList.remove('hidden');
      this.populateCreateCAForm();
    }
  },

  // Close Create CA Modal
  closeCreateCAModal() {
    const modal = document.getElementById('create-ca-modal');
    if (modal) {
      modal.classList.add('hidden');
      document.getElementById('create-ca-form').reset();
    }
  },

  // Populate Create CA Form
  populateCreateCAForm() {
    // Populate academic years
    const academicYearSelect = document.getElementById('ca_new_academic_year');
    if (academicYearSelect && AssessmentManagementComponents.state.academicYears.data) {
      academicYearSelect.innerHTML = '<option value="">Select Academic Year</option>';
      AssessmentManagementComponents.state.academicYears.data.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
        if (year.is_active) option.selected = true;
        academicYearSelect.appendChild(option);
      });
    }

    // Populate terms
    const termSelect = document.getElementById('ca_new_term');
    if (termSelect && AssessmentManagementComponents.state.terms.data) {
      termSelect.innerHTML = '<option value="">Select Term</option>';
      AssessmentManagementComponents.state.terms.data.forEach(term => {
        const option = document.createElement('option');
        option.value = term.id;
        option.textContent = term.name;
        if (term.is_active) option.selected = true;
        termSelect.appendChild(option);
      });
    }

    // Populate subjects
    const subjectSelect = document.getElementById('ca_new_subject');
    if (subjectSelect && AssessmentManagementComponents.state.subjects.data) {
      subjectSelect.innerHTML = '<option value="">Select Subject</option>';
      AssessmentManagementComponents.state.subjects.data.forEach(subject => {
        const option = document.createElement('option');
        option.value = subject.id;
        option.textContent = `${subject.name} (${subject.level.replace('_', '-').toUpperCase()})`;
        subjectSelect.appendChild(option);
      });
    }

    // Populate classes
    const classSelect = document.getElementById('ca_new_class');
    if (classSelect && AssessmentManagementComponents.state.classes.data) {
      classSelect.innerHTML = '<option value="">Select Class</option>';
      AssessmentManagementComponents.state.classes.data.forEach(cls => {
        const option = document.createElement('option');
        option.value = cls.id;
        option.textContent = cls.name + (cls.stream_name ? ` - ${cls.stream_name}` : '');
        classSelect.appendChild(option);
      });
    }

    // Populate teachers
    const teacherSelect = document.getElementById('ca_new_teacher');
    if (teacherSelect && AssessmentManagementComponents.state.teachers.data) {
      teacherSelect.innerHTML = '<option value="">Select Teacher</option>';
      AssessmentManagementComponents.state.teachers.data.forEach(teacher => {
        const option = document.createElement('option');
        option.value = teacher.id;
        option.textContent = `${teacher.first_name} ${teacher.last_name}`;
        teacherSelect.appendChild(option);
      });
    }
  },

  // Create new CA assessment
  async createNewCA() {
    try {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-create-ca', true);
      }

      const formData = new FormData(document.getElementById('create-ca-form'));
      const caData = Object.fromEntries(formData.entries());

      // Add required fields
      caData.assessment_type = 'ca';
      caData.subject_level = 'o_level'; // Default, could be made dynamic
      caData.weight_percentage = 100.00;
      caData.is_active = true;
      caData.created_by_id = this.getCurrentAdminId();

      // Validate required fields
      const requiredFields = ['ca_title', 'ca_method', 'ca_new_academic_year', 'ca_new_term', 'ca_new_subject', 'ca_new_class', 'ca_new_teacher', 'ca_assessment_date'];
      const missingFields = requiredFields.filter(field => !caData[field]);

      if (missingFields.length > 0) {
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show('Please fill in all required fields', 'error');
        }
        return;
      }

      // Map form fields to API fields
      const assessmentData = {
        title: caData.ca_title,
        assessment_type: caData.assessment_type,
        ca_method: caData.ca_method,
        academic_year_id: caData.ca_new_academic_year,
        term_id: caData.ca_new_term,
        subject_id: caData.ca_new_subject,
        class_id: caData.ca_new_class,
        teacher_id: caData.ca_new_teacher,
        assessment_date: caData.ca_assessment_date,
        due_date: caData.ca_due_date || null,
        subject_level: caData.subject_level,
        weight_percentage: caData.weight_percentage,
        is_active: caData.is_active,
        created_by_id: caData.created_by_id
      };

      // Create the assessment
      const result = await window.AssessmentsAPI.create(assessmentData);

      if (result.success) {
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show('CA Assessment created successfully!', 'success');
        }

        this.closeCreateCAModal();

        // Refresh the assessment dropdown
        await AssessmentManagementComponents.loadInitialData();
        this.populateDropdowns();
        this.updateAssessmentDropdown();
      } else {
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show(result.message || 'Failed to create CA assessment', 'error');
        }
      }
    } catch (error) {
      console.error('Create CA error:', error);
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to create CA assessment', 'error');
      }
    } finally {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-create-ca', false);
      }
    }
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Add event listeners for dropdown changes
    ['ca_academic_year', 'ca_term', 'ca_subject'].forEach(selectId => {
      const select = document.getElementById(selectId);
      if (select) {
        select.addEventListener('change', () => {
          this.updateAssessmentDropdown();
        });
      }
    });

    // Add event listener for Create CA form submission
    const createCAForm = document.getElementById('create-ca-form');
    if (createCAForm) {
      createCAForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.createNewCA();
      });
    }
  },

  // Update assessment dropdown based on other selections
  updateAssessmentDropdown() {
    const academicYear = document.getElementById('ca_academic_year').value;
    const term = document.getElementById('ca_term').value;
    const subject = document.getElementById('ca_subject').value;

    const assessmentSelect = document.getElementById('ca_assessment');
    if (!assessmentSelect) return;

    assessmentSelect.innerHTML = '<option value="">Select Assessment</option>';

    if (academicYear && term && subject) {
      // Filter assessments based on selections
      const filteredAssessments = AssessmentManagementComponents.state.assessments.data?.filter(assessment =>
        assessment.academic_year_id == academicYear &&
        assessment.term_id == term &&
        assessment.subject_id == subject &&
        assessment.assessment_type !== 'Term Exam'
      ) || [];

      filteredAssessments.forEach(assessment => {
        const option = document.createElement('option');
        option.value = assessment.id;
        option.textContent = assessment.assessment_name;
        assessmentSelect.appendChild(option);
      });
    }
  }
};

// Enter Exam Grades Component
const EnterExamGradesComponent = {
  // Component state
  state: {
    selectedExam: null,
    students: [],
    grades: {}
  },

  // Render enter exam grades interface
  render() {
    return `
      <div class="space-y-6">
        ${AIMSDesignSystem.layouts.pageHeader(
          'Enter Exam Grades',
          'Input term examination marks and calculate final grades'
        )}

        <!-- Exam Selection -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">Select Examination</h3>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            ${AIMSDesignSystem.forms.select('exam_academic_year', 'Academic Year', [], '')}
            ${AIMSDesignSystem.forms.select('exam_term', 'Term', [], '')}
            ${AIMSDesignSystem.forms.select('exam_subject', 'Subject', [], '')}
            ${AIMSDesignSystem.forms.select('exam_class', 'Class', [], '')}
          </div>
          <div class="mt-4">
            ${AIMSDesignSystem.forms.button('load-exam-students', 'Load Students', 'primary', {
              onclick: 'EnterExamGradesComponent.loadStudents()'
            })}
          </div>
        </div>

        <!-- Exam Details -->
        <div id="exam-details" class="hidden bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <!-- Exam details will be populated here -->
        </div>

        <!-- Grades Entry -->
        <div id="grades-entry-section" class="hidden bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900">Enter Exam Marks</h3>
              <div class="flex items-center space-x-4">
                ${AIMSDesignSystem.forms.button('calculate-grades', 'Calculate Final Grades', 'secondary', {
                  onclick: 'EnterExamGradesComponent.calculateFinalGrades()'
                })}
                ${AIMSDesignSystem.forms.button('save-grades', 'Save All Grades', 'primary', {
                  onclick: 'EnterExamGradesComponent.saveGrades()'
                })}
              </div>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admission No.</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">CA Average</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">CA %</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Exam Mark (%)</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Final Score</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Comments</th>
                </tr>
              </thead>
              <tbody id="grades-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Students and grades will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize component
  async init() {
    await AssessmentManagementComponents.loadInitialData();
    this.populateDropdowns();
    this.initializeEventListeners();
  },

  // Populate dropdowns
  populateDropdowns() {
    // Populate academic years
    const academicYearSelect = document.getElementById('exam_academic_year');
    if (academicYearSelect) {
      academicYearSelect.innerHTML = '<option value="">Select Academic Year</option>';
      AssessmentManagementComponents.state.academicYears.data?.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
        if (year.is_active) option.selected = true;
        academicYearSelect.appendChild(option);
      });
    }

    // Populate terms
    const termSelect = document.getElementById('exam_term');
    if (termSelect) {
      termSelect.innerHTML = '<option value="">Select Term</option>';
      AssessmentManagementComponents.state.terms.data?.forEach(term => {
        const option = document.createElement('option');
        option.value = term.id;
        option.textContent = term.name;
        if (term.is_active) option.selected = true;
        termSelect.appendChild(option);
      });
    }

    // Populate subjects
    const subjectSelect = document.getElementById('exam_subject');
    if (subjectSelect) {
      subjectSelect.innerHTML = '<option value="">Select Subject</option>';
      AssessmentManagementComponents.state.subjects.data?.forEach(subject => {
        const option = document.createElement('option');
        option.value = subject.id;
        option.textContent = `${subject.name} (${subject.level.replace('_', '-').toUpperCase()})`;
        subjectSelect.appendChild(option);
      });
    }

    // Populate classes
    const classSelect = document.getElementById('exam_class');
    if (classSelect) {
      classSelect.innerHTML = '<option value="">Select Class</option>';
      AssessmentManagementComponents.state.classes.data?.forEach(cls => {
        const option = document.createElement('option');
        option.value = cls.id;
        option.textContent = cls.name + (cls.stream_name ? ` - ${cls.stream_name}` : '');
        classSelect.appendChild(option);
      });
    }
  },

  // Load students for selected exam
  async loadStudents() {
    const academicYear = document.getElementById('exam_academic_year').value;
    const term = document.getElementById('exam_term').value;
    const subject = document.getElementById('exam_subject').value;
    const classId = document.getElementById('exam_class').value;

    if (!academicYear || !term || !subject || !classId) {
      AIMSDesignSystem.notifications.show('Please select all fields', 'error');
      return;
    }

    try {
      // Use the API service
      const result = await ExamGradesAPI.getStudents({
        academic_year: academicYear,
        term: term,
        subject: subject,
        class: classId
      });

      if (result.success) {
        this.state.students = result.data.students;
        this.state.selectedExam = result.data.examInfo;
        this.renderExamDetails();
        this.renderGradesTable();
        document.getElementById('exam-details').classList.remove('hidden');
        document.getElementById('grades-entry-section').classList.remove('hidden');
      } else {
        AIMSDesignSystem.notifications.show(result.message || 'Failed to load students', 'error');
      }
    } catch (error) {
      console.error('Load students error:', error);
      AIMSDesignSystem.notifications.show('Failed to load students', 'error');
    }
  },

  // Render exam details
  renderExamDetails() {
    const container = document.getElementById('exam-details');
    if (!container || !this.state.selectedExam) return;

    const exam = this.state.selectedExam;
    container.innerHTML = `
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700">Subject</label>
          <p class="mt-1 text-sm text-gray-900">${exam.subject_name}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Class</label>
          <p class="mt-1 text-sm text-gray-900">${exam.class_name}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Term</label>
          <p class="mt-1 text-sm text-gray-900">${exam.term_name}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Students</label>
          <p class="mt-1 text-sm text-gray-900">${this.state.students.length}</p>
        </div>
      </div>
      <div class="mt-4 p-4 bg-blue-50 rounded-lg">
        <h4 class="font-medium text-blue-900 mb-2">Grading Information</h4>
        <p class="text-sm text-blue-700">
          Final Score = CA Percentage (20%) + Exam Mark (80%)<br>
          CA Percentage = (Average CA Score ÷ 3) × 20<br>
          Final grades are calculated based on the grade boundaries.
        </p>
      </div>
    `;
  },

  // Render grades table
  renderGradesTable() {
    const tbody = document.getElementById('grades-table-body');
    if (!tbody) return;

    tbody.innerHTML = this.state.students.map(student => `
      <tr>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <img class="h-8 w-8 rounded-full object-cover"
                 src="${student.passport_photo || '/assets/images/default-avatar.png'}"
                 alt="${student.first_name} ${student.last_name}">
            <div class="ml-3">
              <div class="text-sm font-medium text-gray-900">${student.first_name} ${student.last_name}</div>
            </div>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${student.admission_number}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
          ${student.ca_average ? student.ca_average.toFixed(2) : 'N/A'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
          ${student.ca_percentage ? student.ca_percentage.toFixed(1) + '%' : 'N/A'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <input type="number"
                 id="exam_mark_${student.id}"
                 min="0"
                 max="100"
                 step="0.1"
                 value="${student.current_exam_mark || ''}"
                 class="w-20 px-3 py-2 border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                 onchange="EnterExamGradesComponent.updateExamMark(${student.id}, this.value)">
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <span id="final_score_${student.id}" class="text-sm font-medium text-gray-900">
            ${student.final_score ? student.final_score.toFixed(1) + '%' : '-'}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <span id="final_grade_${student.id}" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getGradeBadgeClass(student.final_grade)}">
            ${student.final_grade || '-'}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <input type="text"
                 id="exam_comment_${student.id}"
                 value="${student.current_comment || ''}"
                 placeholder="Optional comment..."
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                 onchange="EnterExamGradesComponent.updateComment(${student.id}, this.value)">
        </td>
      </tr>
    `).join('');
  },

  // Update exam mark for student
  updateExamMark(studentId, examMark) {
    if (!this.state.grades[studentId]) {
      this.state.grades[studentId] = {};
    }
    this.state.grades[studentId].exam_mark = examMark ? parseFloat(examMark) : null;

    // Calculate final score if CA percentage is available
    const student = this.state.students.find(s => s.id === studentId);
    if (student && student.ca_percentage && examMark) {
      const finalScore = student.ca_percentage + parseFloat(examMark);
      this.state.grades[studentId].final_score = finalScore;

      // Update display
      const finalScoreElement = document.getElementById(`final_score_${studentId}`);
      if (finalScoreElement) {
        finalScoreElement.textContent = finalScore.toFixed(1) + '%';
      }

      // Calculate and update grade
      const grade = this.calculateGrade(finalScore);
      this.state.grades[studentId].final_grade = grade;

      const gradeElement = document.getElementById(`final_grade_${studentId}`);
      if (gradeElement) {
        gradeElement.textContent = grade;
        gradeElement.className = `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getGradeBadgeClass(grade)}`;
      }
    }
  },

  // Update comment for student
  updateComment(studentId, comment) {
    if (!this.state.grades[studentId]) {
      this.state.grades[studentId] = {};
    }
    this.state.grades[studentId].comment = comment;
  },

  // Calculate grade based on final score
  calculateGrade(finalScore) {
    // This should use the grade boundaries from the database
    // For now, using standard boundaries
    if (finalScore >= 80) return 'A';
    if (finalScore >= 70) return 'B';
    if (finalScore >= 60) return 'C';
    if (finalScore >= 50) return 'D';
    return 'E';
  },

  // Get grade badge CSS class
  getGradeBadgeClass(grade) {
    const classes = {
      'A': 'bg-green-100 text-green-800',
      'B': 'bg-blue-100 text-blue-800',
      'C': 'bg-yellow-100 text-yellow-800',
      'D': 'bg-orange-100 text-orange-800',
      'E': 'bg-red-100 text-red-800'
    };
    return classes[grade] || 'bg-gray-100 text-gray-800';
  },

  // Calculate final grades for all students
  calculateFinalGrades() {
    let calculatedCount = 0;

    this.state.students.forEach(student => {
      const examMarkInput = document.getElementById(`exam_mark_${student.id}`);
      if (examMarkInput && examMarkInput.value && student.ca_percentage) {
        this.updateExamMark(student.id, examMarkInput.value);
        calculatedCount++;
      }
    });

    if (calculatedCount > 0) {
      AIMSDesignSystem.notifications.show(`Final grades calculated for ${calculatedCount} students`, 'success');
    } else {
      AIMSDesignSystem.notifications.show('No exam marks found to calculate grades', 'warning');
    }
  },

  // Save grades
  async saveGrades() {
    if (!this.state.selectedExam) {
      if (window.showNotification) {
        window.showNotification('No exam selected', 'error');
      } else {
        alert('Error: No exam selected');
      }
      return;
    }

    // Get current admin ID for tracking
    const currentAdminId = AssessmentManagementComponents.getCurrentAdminId();

    const gradesData = Object.entries(this.state.grades).map(([studentId, data]) => ({
      student_id: parseInt(studentId),
      subject_id: this.state.selectedExam.subject_id,
      academic_year_id: this.state.selectedExam.academic_year_id,
      term_id: this.state.selectedExam.term_id,
      exam_mark: data.exam_mark,
      final_score: data.final_score,
      final_grade: data.final_grade,
      comment: data.comment || null,
      created_by_id: currentAdminId, // Include system admin tracking
      updated_by_id: currentAdminId
    })).filter(item => item.exam_mark !== null);

    if (gradesData.length === 0) {
      if (window.showNotification) {
        window.showNotification('No grades to save', 'error');
      } else {
        alert('Error: No grades to save');
      }
      return;
    }

    try {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-grades', true);
      }

      // Use the API service
      const result = await ExamGradesAPI.create(gradesData);

      if (result.success) {
        if (window.showNotification) {
          window.showNotification(`${gradesData.length} grades saved successfully!`, 'success');
        } else {
          alert(`${gradesData.length} grades saved successfully!`);
        }
        this.state.grades = {};
      } else {
        if (window.showNotification) {
          window.showNotification(result.message || 'Failed to save grades', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to save grades'));
        }
      }
    } catch (error) {
      console.error('Save grades error:', error);
      if (window.showNotification) {
        window.showNotification('Failed to save grades', 'error');
      } else {
        alert('Error: Failed to save grades');
      }
    } finally {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-grades', false);
      }
    }
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Add event listeners for dropdown changes if needed
  }
};

// Export components to global scope
window.EnterCAScoresComponent = EnterCAScoresComponent;
window.EnterExamGradesComponent = EnterExamGradesComponent;
window.AssessmentManagementComponents = AssessmentManagementComponents;
