// AIMS - Authentication Module
// Handles user authentication and session management

// Check if this is the first time the default admin is logging in
async function checkFirstTimeSetup() {
  try {
    console.log('🔍 Checking for first-time setup requirements...');

    // Check if academic years exist
    const response = await fetch(`${AIMS.serverUrl}/api/academic/years`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('aims_token')}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      const academicYears = result.data || result || [];

      if (academicYears.length === 0) {
        console.log('📅 No academic years found - showing setup modal for first-time admin');

        // Wait for the modal system to be available
        let attempts = 0;
        const maxAttempts = 10;

        const showSetupModal = () => {
          if (window.showAcademicYearSetupModal && typeof window.showAcademicYearSetupModal === 'function') {
            console.log('✅ Showing academic year setup modal for first-time admin');
            window.showAcademicYearSetupModal();
          } else {
            attempts++;
            if (attempts < maxAttempts) {
              console.log(`⏳ Waiting for modal system... (${attempts}/${maxAttempts})`);
              setTimeout(showSetupModal, 200);
            } else {
              console.error('❌ Modal system not available after maximum attempts');
              alert('Welcome! Please set up your academic year and terms in the Academic Management section.');
            }
          }
        };

        showSetupModal();
      } else {
        console.log('✅ Academic years already configured');
      }
    } else {
      console.warn('⚠️ Could not check academic years status');
    }

  } catch (error) {
    console.error('❌ Error checking first-time setup:', error);
    // Don't show error to user, just log it
  }
}

// Authentication manager
const AuthManager = {
  // Login form handler
  initializeLoginForm() {
    const loginForm = document.getElementById('login-form');
    if (!loginForm) {
      console.warn('Login form not found!');
      return;
    }

    console.log('✅ Login form found, adding event listener...');

    loginForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      console.log('🔄 Login form submitted');
      await this.handleLogin(e.target);
    });
  },
  
  // Handle login
  async handleLogin(form) {
    console.log('🔄 Handling login...');
    const formData = new FormData(form);
    const username = formData.get('username');
    const password = formData.get('password');

    console.log('📝 Form data:', { username: username ? '***' : 'empty', password: password ? '***' : 'empty' });

    // Validate inputs
    if (!username || !password) {
      console.warn('❌ Validation failed: missing username or password');
      this.showNotification('Please enter both username and password', 'error');
      return;
    }

    try {
      // Show loading
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;
      submitBtn.textContent = 'Signing in...';
      submitBtn.disabled = true;

      // Attempt login
      const response = await AuthAPI.login({ username, password });

      if (response.success) {
        // Store token (check both response.token and response.data.token)
        const token = response.token || response.data?.token;
        if (token) {
          localStorage.setItem('aims_token', token);
        }

        // Store user data (check both response.user and response.data.user)
        const user = response.user || response.data?.user;
        if (user) {
          AIMS.currentUser = user;
        }

        // Enable application menu
        if (window.require) {
          const { ipcRenderer } = window.require('electron');
          await ipcRenderer.invoke('login-success');
        }

        // Debug: Check if token was stored correctly
        const storedToken = localStorage.getItem('aims_token');
        console.log('🔍 Token check after login:', storedToken ? 'Token stored successfully' : 'Token NOT stored');

        // Small delay to ensure token is properly stored
        await new Promise(resolve => setTimeout(resolve, 100));

        // Redirect to main application
        await showMainApplication();

        // MANDATORY: Enforce academic year setup after login
        setTimeout(async () => {
          console.log('🔍 MANDATORY: Enforcing academic year setup after login...');
          await this.enforceAcademicYearSetup();

          // Start persistent checking for all users (until academic setup is complete)
          if (AIMS.currentUser) {
            console.log('🔄 Starting persistent academic check for user...');
            setTimeout(() => {
              if (window.startPersistentAcademicCheck && typeof window.startPersistentAcademicCheck === 'function') {
                window.startPersistentAcademicCheck();
              }
            }, 2000); // Start after initial check
          }
        }, 500); // Faster response for immediate check

      } else {
        throw new Error(response.message || 'Login failed');
      }

    } catch (error) {
      console.error('Login error:', error);
      this.showNotification(error.message || 'Login failed. Please try again.', 'error');

      // Reset form
      const submitBtn = form.querySelector('button[type="submit"]');
      if (submitBtn) {
        submitBtn.textContent = 'Sign In';
        submitBtn.disabled = false;
      }
    }
  },

  // Enforce academic year setup - MANDATORY check after login
  async enforceAcademicYearSetup() {
    console.log('🎯 ENFORCING academic year setup check...');

    let attempts = 0;
    const maxAttempts = 20; // More attempts for reliability

    const checkFunction = async () => {
      // First, check if the academic setup function is available
      if (window.checkAcademicYearsSetup && typeof window.checkAcademicYearsSetup === 'function') {
        console.log('✅ Academic year setup function found, executing...');

        // Call the function and wait for it to complete
        try {
          await window.checkAcademicYearsSetup();
          console.log('✅ Academic year setup check completed');
        } catch (error) {
          console.error('❌ Error during academic year setup check:', error);
          this.fallbackAcademicSetup();
        }

      } else if (window.showAcademicYearSetupModal && typeof window.showAcademicYearSetupModal === 'function') {
        // Direct fallback to modal function
        console.log('✅ Academic setup modal function found, executing...');
        try {
          await window.showAcademicYearSetupModal();
          console.log('✅ Academic setup modal shown');
        } catch (error) {
          console.error('❌ Error showing academic setup modal:', error);
          this.fallbackAcademicSetup();
        }

      } else {
        attempts++;
        if (attempts < maxAttempts) {
          console.log(`⏳ Waiting for academic setup functions... (${attempts}/${maxAttempts})`);
          setTimeout(checkFunction, 300); // Slightly longer delay for reliability
        } else {
          console.error('❌ Academic setup functions not available after maximum attempts');
          this.fallbackAcademicSetup();
        }
      }
    };

    // Start the check immediately
    checkFunction();
  },

  // Fallback academic setup for when functions are not available
  fallbackAcademicSetup() {
    console.log('🚨 Using fallback academic setup method');

    const isSystemAdmin = AIMS.currentUser && AIMS.currentUser.role === 'system_admin';
    const isDefaultAdmin = AIMS.currentUser && AIMS.currentUser.username === 'admin' && AIMS.currentUser.role === 'system_admin';

    if (isDefaultAdmin) {
      alert('CRITICAL: Academic Year Setup Required!\n\nAs the system administrator, you MUST configure the academic year and terms before the system can be used.\n\nPlease go to Academic Management → Academic Years to set up your academic year and terms.\n\nThe system cannot function without this configuration.');
    } else if (isSystemAdmin) {
      alert('CRITICAL: Academic Year Setup Required!\n\nThe system cannot be used without configuring the academic year and terms.\n\nPlease go to Academic Management → Academic Years to complete the setup.');
    } else {
      alert('NOTICE: Academic Year Setup Required!\n\nThe system administrator must configure the academic year and terms before the system can be used.\n\nPlease contact your system administrator.');
    }
  },

  // Logout
  async logout() {
    try {
      // Call logout API
      await AuthAPI.logout();
    } catch (error) {
      console.error('Logout API error:', error);
      // Continue with logout even if API call fails
    }

    // Stop persistent academic check
    if (window.stopPersistentAcademicCheck && typeof window.stopPersistentAcademicCheck === 'function') {
      window.stopPersistentAcademicCheck();
    }

    // Clear local storage
    localStorage.removeItem('aims_token');

    // Clear current user
    AIMS.currentUser = null;
    AIMS.isInitialized = false;

    // Hide application menu
    if (window.require) {
      const { ipcRenderer } = window.require('electron');
      await ipcRenderer.invoke('logout');
    }

    // Show notification
    this.showNotification('You have been logged out successfully.', 'info');

    // Redirect to login
    showLoginScreen();
  },
  
  // Check if user is authenticated
  isAuthenticated() {
    return !!localStorage.getItem('aims_token') && !!AIMS.currentUser;
  },
  
  // Get current user
  getCurrentUser() {
    return AIMS.currentUser;
  },
  
  // Check if user has specific role
  hasRole(role) {
    return AIMS.currentUser && AIMS.currentUser.role === role;
  },
  
  // Check if user has any of the specified roles
  hasAnyRole(roles) {
    return AIMS.currentUser && roles.includes(AIMS.currentUser.role);
  },
  
  // Get user's full name
  getUserFullName() {
    if (!AIMS.currentUser) return 'Unknown User';
    
    const { first_name, middle_name, last_name } = AIMS.currentUser;
    let fullName = first_name || '';
    
    if (middle_name) {
      fullName += ` ${middle_name}`;
    }
    
    if (last_name) {
      fullName += ` ${last_name}`;
    }
    
    return fullName.trim() || 'Unknown User';
  },
  
  // Get user's role display name
  getRoleDisplayName() {
    if (!AIMS.currentUser) return 'Unknown Role';
    
    const roleNames = {
      'system_admin': 'System Administrator',
      'class_teacher': 'Class Teacher',
      'subject_teacher': 'Subject Teacher'
    };
    
    return roleNames[AIMS.currentUser.role] || 'Unknown Role';
  },
  
  // Initialize logout button
  initializeLogoutButton() {
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
      logoutBtn.addEventListener('click', (e) => {
        e.preventDefault();
        UIUtils.showConfirm('Are you sure you want to logout?', () => {
          this.logout();
        });
      });
    }
  },
  
  // Initialize user profile dropdown
  initializeUserProfile() {
    const userProfile = document.getElementById('user-profile');
    const userDropdown = document.getElementById('user-dropdown');
    
    if (userProfile && userDropdown) {
      userProfile.addEventListener('click', (e) => {
        e.preventDefault();
        userDropdown.classList.toggle('show');
      });
      
      // Close dropdown when clicking outside
      document.addEventListener('click', (e) => {
        if (!userProfile.contains(e.target)) {
          userDropdown.classList.remove('show');
        }
      });
    }
  },
  
  // Update user interface with current user info
  updateUserInterface() {
    if (!AIMS.currentUser) return;
    
    // Update user name display
    const userNameElements = document.querySelectorAll('.user-name');
    userNameElements.forEach(element => {
      element.textContent = this.getUserFullName();
    });
    
    // Update user role display
    const userRoleElements = document.querySelectorAll('.user-role');
    userRoleElements.forEach(element => {
      element.textContent = this.getRoleDisplayName();
    });
    
    // Update user email display
    const userEmailElements = document.querySelectorAll('.user-email');
    userEmailElements.forEach(element => {
      element.textContent = AIMS.currentUser.email || '';
    });
    
    // Update profile picture if available
    const profilePicElements = document.querySelectorAll('.user-profile-pic');
    profilePicElements.forEach(element => {
      if (AIMS.currentUser.profile_picture) {
        element.src = AIMS.currentUser.profile_picture;
      } else {
        // Use default avatar based on role
        const defaultAvatars = {
          'system_admin': '../assets/images/admin-avatar.png',
          'class_teacher': '../assets/images/teacher-avatar.png',
          'subject_teacher': '../assets/images/teacher-avatar.png'
        };
        element.src = defaultAvatars[AIMS.currentUser.role] || '../assets/images/default-avatar.png';
      }
    });

    // Update academic information in navbar
    this.updateAcademicInfo();
  },

  // Update academic information in navbar
  updateAcademicInfo() {
    const dateElement = document.getElementById('navbar-date');
    const academicYearElement = document.getElementById('navbar-academic-year');

    // Update date and time
    const currentTime = new Date().toLocaleString('en-UG', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    if (dateElement) {
      dateElement.textContent = currentTime;
    }

    if (academicYearElement) {
      // Check if academic data is available from AIMS global object
      const currentYear = window.AIMS?.currentAcademicYear;
      const currentTerm = window.AIMS?.currentTerm;

      if (currentYear && currentTerm) {
        academicYearElement.textContent = `Academic Year ${currentYear} - ${currentTerm}`;
        academicYearElement.classList.remove('text-yellow-600');
      } else {
        academicYearElement.textContent = 'Academic Setup Required';
        academicYearElement.classList.add('text-yellow-600');
      }
    }
  },
  
  // Show notification with fallback
  showNotification(message, type = 'info') {
    // Use global notification system first
    if (window.showNotification && typeof window.showNotification === 'function') {
      window.showNotification(message, type);
    } else if (window.UIUtils && typeof UIUtils.showNotification === 'function') {
      UIUtils.showNotification(message, type);
    } else {
      // Fallback to console and alert
      console.log(`${type.toUpperCase()}: ${message}`);
      if (type === 'error') {
        alert(`Error: ${message}`);
      } else if (type === 'success') {
        alert(`Success: ${message}`);
      }
    }
  },

  // Initialize all authentication components
  initialize() {
    this.initializeLoginForm();
    this.initializeLogoutButton();
    this.initializeUserProfile();

    // Update UI if user is already authenticated
    if (this.isAuthenticated()) {
      this.updateUserInterface();
    }
  }
};

// Session management
const SessionManager = {
  // Check session validity
  async checkSession() {
    try {
      const token = localStorage.getItem('aims_token');
      if (!token) return false;

      const response = await AuthAPI.validateToken(token);
      if (response.success) {
        AIMS.currentUser = response.user;
        return true;
      }
    } catch (error) {
      console.error('Session validation failed:', error);
    }
    
    // Clear invalid session
    localStorage.removeItem('aims_token');
    AIMS.currentUser = null;
    return false;
  },
  
  // Refresh session periodically
  startSessionRefresh() {
    // Check session every 30 minutes
    setInterval(async () => {
      if (AuthManager.isAuthenticated()) {
        const isValid = await this.checkSession();
        if (!isValid) {
          UIUtils.showNotification('Your session has expired. Please login again.', 'warning');
          AuthManager.logout();
        }
      }
    }, 30 * 60 * 1000); // 30 minutes
  }
};

// Initialize authentication when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('🔧 DOM loaded, initializing AuthManager...');
  AuthManager.initialize();
  SessionManager.startSessionRefresh();

  // Add a test function to help debug
  window.testLogin = function() {
    console.log('🧪 Testing login form...');
    const form = document.getElementById('login-form');
    const username = document.getElementById('username');
    const password = document.getElementById('password');

    console.log('Form found:', !!form);
    console.log('Username field found:', !!username);
    console.log('Password field found:', !!password);
    console.log('Username value:', username?.value || 'empty');
    console.log('Password value:', password?.value ? '***' : 'empty');

    if (form && username && password) {
      console.log('✅ All form elements found');
      if (username.value && password.value) {
        console.log('🚀 Triggering login...');
        AuthManager.handleLogin(form);
      } else {
        console.log('❌ Please enter username and password');
        alert('Please enter both username and password');
      }
    } else {
      console.log('❌ Form elements missing');
      alert('Form elements not found');
    }
  };

  // Add a quick login test function
  window.quickLogin = function() {
    console.log('🚀 Quick login test with default credentials...');
    const username = document.getElementById('username');
    const password = document.getElementById('password');

    if (username && password) {
      username.value = 'admin';
      password.value = 'admin123';
      console.log('✅ Credentials filled, triggering login...');

      const form = document.getElementById('login-form');
      if (form) {
        AuthManager.handleLogin(form);
      } else {
        console.error('❌ Login form not found');
      }
    } else {
      console.error('❌ Username or password field not found');
    }
  };
});

// Toggle password visibility
function togglePassword() {
  const passwordInput = document.getElementById('password');
  const toggleButton = document.querySelector('.password-toggle');
  const toggleIcon = toggleButton.querySelector('i');

  if (passwordInput.type === 'password') {
    passwordInput.type = 'text';
    toggleIcon.classList.remove('fa-eye');
    toggleIcon.classList.add('fa-eye-slash');
  } else {
    passwordInput.type = 'password';
    toggleIcon.classList.remove('fa-eye-slash');
    toggleIcon.classList.add('fa-eye');
  }
}

// Show forgot password modal
function showForgotPassword() {
  if (window.UIUtils) {
    UIUtils.showNotification('Forgot Password feature coming soon!', 'info');
  } else {
    alert('Forgot Password feature coming soon!');
  }
}

// Export to global scope
window.AuthManager = AuthManager;
window.SessionManager = SessionManager;
window.togglePassword = togglePassword;
window.showForgotPassword = showForgotPassword;
