const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get all A-Level combinations
router.get('/', async (req, res) => {
  try {
    const query = `
      SELECT
        alc.*,
        s.name as subsidiary_subject_name,
        s.short_name as subsidiary_subject_short_name
      FROM a_level_combinations alc
      LEFT JOIN a_level_subjects s ON alc.subsidiary_subject_id = s.id
      ORDER BY alc.combination_type, alc.name
    `;
    
    const result = await executeQuery(query);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get A-Level combinations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level combinations'
    });
  }
});

// Get A-Level combination by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT 
        alc.*,
        s.name as subsidiary_subject_name,
        s.short_name as subsidiary_subject_short_name
      FROM a_level_combinations alc
      LEFT JOIN subjects s ON alc.subsidiary_subject_id = s.id
      WHERE alc.id = ?
    `;
    
    const result = await executeQuery(query, [id]);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'A-Level combination not found'
      });
    }

    // Get principal subjects for this combination
    const principalQuery = `
      SELECT 
        s.id, s.name, s.short_name, s.uneb_code
      FROM a_level_combination_subjects alcs
      JOIN subjects s ON alcs.subject_id = s.id
      WHERE alcs.combination_id = ? AND alcs.subject_type = 'principal'
      ORDER BY s.name
    `;
    
    const principalResult = await executeQuery(principalQuery, [id]);

    const combination = result.data[0];
    combination.principal_subjects = principalResult.success ? principalResult.data : [];

    res.json({
      success: true,
      data: combination
    });

  } catch (error) {
    console.error('Get A-Level combination error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve A-Level combination'
    });
  }
});

// Create A-Level combination
router.post('/', async (req, res) => {
  try {
    const { name, short_name, combination_type, subsidiary_subject_id, principal_subjects } = req.body;

    // Validate required fields
    if (!name || !short_name || !combination_type) {
      return res.status(400).json({
        success: false,
        message: 'Name, short name, and combination type are required'
      });
    }

    // Check if short name already exists
    const checkQuery = 'SELECT id FROM a_level_combinations WHERE short_name = ?';
    const checkResult = await executeQuery(checkQuery, [short_name]);
    
    if (!checkResult.success) {
      throw new Error(checkResult.error);
    }

    if (checkResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Short name already exists'
      });
    }

    // Get current user ID for audit fields
    const userId = req.user?.id || 1; // Default to system admin if no user context

    // Insert combination
    const insertQuery = `
      INSERT INTO a_level_combinations (
        name, short_name, combination_type, subsidiary_subject_id, is_active, created_by_id, updated_by_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, TRUE, ?, NULL, NOW(), NOW())
    `;

    const insertResult = await executeQuery(insertQuery, [name, short_name, combination_type, subsidiary_subject_id, userId]);

    if (!insertResult.success) {
      throw new Error(insertResult.error);
    }

    const combinationId = insertResult.data.insertId;

    // Insert principal subjects if provided
    if (principal_subjects && Array.isArray(principal_subjects) && principal_subjects.length > 0) {
      const subjectPromises = principal_subjects.map(subjectId => {
        const subjectQuery = `
          INSERT INTO a_level_combination_subjects (combination_id, subject_id, subject_type, created_at, updated_at)
          VALUES (?, ?, 'principal', NOW(), NOW())
        `;
        return executeQuery(subjectQuery, [combinationId, subjectId]);
      });

      await Promise.all(subjectPromises);
    }

    // Get the created combination with related data
    const newCombinationQuery = `
      SELECT 
        alc.*,
        s.name as subsidiary_subject_name
      FROM a_level_combinations alc
      LEFT JOIN subjects s ON alc.subsidiary_subject_id = s.id
      WHERE alc.id = ?
    `;

    const newCombinationResult = await executeQuery(newCombinationQuery, [combinationId]);

    res.status(201).json({
      success: true,
      message: 'A-Level combination created successfully',
      data: newCombinationResult.data[0]
    });

  } catch (error) {
    console.error('Create A-Level combination error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create A-Level combination'
    });
  }
});

// Update A-Level combination
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, short_name, combination_type, subsidiary_subject_id, is_active, principal_subjects } = req.body;

    // Check if combination exists
    const checkQuery = 'SELECT id FROM a_level_combinations WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);
    
    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'A-Level combination not found'
      });
    }

    // Check if short name already exists for other combinations
    const duplicateQuery = 'SELECT id FROM a_level_combinations WHERE short_name = ? AND id != ?';
    const duplicateResult = await executeQuery(duplicateQuery, [short_name, id]);
    
    if (!duplicateResult.success) {
      throw new Error(duplicateResult.error);
    }

    if (duplicateResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Short name already exists'
      });
    }

    // Get current user ID for audit fields
    const userId = req.user?.id || 1; // Default to system admin if no user context

    // Update combination
    const updateQuery = `
      UPDATE a_level_combinations SET
        name = ?, short_name = ?, combination_type = ?, subsidiary_subject_id = ?, is_active = ?, updated_by_id = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const updateResult = await executeQuery(updateQuery, [name, short_name, combination_type, subsidiary_subject_id, is_active, userId, id]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    // Update principal subjects if provided
    if (principal_subjects && Array.isArray(principal_subjects)) {
      // Delete existing principal subjects
      await executeQuery('DELETE FROM a_level_combination_subjects WHERE combination_id = ? AND subject_type = "principal"', [id]);

      // Insert new principal subjects
      if (principal_subjects.length > 0) {
        const subjectPromises = principal_subjects.map(subjectId => {
          const subjectQuery = `
            INSERT INTO a_level_combination_subjects (combination_id, subject_id, subject_type, created_at, updated_at)
            VALUES (?, ?, 'principal', NOW(), NOW())
          `;
          return executeQuery(subjectQuery, [id, subjectId]);
        });

        await Promise.all(subjectPromises);
      }
    }

    // Get updated combination
    const updatedCombinationQuery = `
      SELECT 
        alc.*,
        s.name as subsidiary_subject_name
      FROM a_level_combinations alc
      LEFT JOIN subjects s ON alc.subsidiary_subject_id = s.id
      WHERE alc.id = ?
    `;

    const updatedCombinationResult = await executeQuery(updatedCombinationQuery, [id]);

    res.json({
      success: true,
      message: 'A-Level combination updated successfully',
      data: updatedCombinationResult.data[0]
    });

  } catch (error) {
    console.error('Update A-Level combination error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update A-Level combination'
    });
  }
});

// Delete A-Level combination
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if combination exists
    const checkQuery = 'SELECT id FROM a_level_combinations WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);
    
    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'A-Level combination not found'
      });
    }

    // Delete combination subjects first (due to foreign key constraints)
    await executeQuery('DELETE FROM a_level_combination_subjects WHERE combination_id = ?', [id]);

    // Delete combination
    const deleteQuery = 'DELETE FROM a_level_combinations WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'A-Level combination deleted successfully'
    });

  } catch (error) {
    console.error('Delete A-Level combination error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete A-Level combination'
    });
  }
});

// Toggle A-Level combination status
router.put('/:id/toggle-status', async (req, res) => {
  try {
    const { id } = req.params;

    // Get current status
    const currentQuery = 'SELECT is_active FROM a_level_combinations WHERE id = ?';
    const currentResult = await executeQuery(currentQuery, [id]);
    
    if (!currentResult.success || currentResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'A-Level combination not found'
      });
    }

    const currentStatus = currentResult.data[0].is_active;
    const newStatus = !currentStatus;

    // Update status
    const updateQuery = 'UPDATE a_level_combinations SET is_active = ?, updated_at = NOW() WHERE id = ?';
    const updateResult = await executeQuery(updateQuery, [newStatus, id]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    res.json({
      success: true,
      message: `A-Level combination ${newStatus ? 'activated' : 'deactivated'} successfully`,
      data: { is_active: newStatus }
    });

  } catch (error) {
    console.error('Toggle A-Level combination status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to toggle A-Level combination status'
    });
  }
});

// Get subjects for a specific combination
router.get('/:id/subjects', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if combination exists
    const checkQuery = 'SELECT id, name FROM a_level_combinations WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'A-Level combination not found'
      });
    }

    // Get all subjects for this combination (both principal and subsidiary)
    const subjectsQuery = `
      SELECT
        s.id, s.name, s.short_name, s.uneb_code, s.subject_type,
        alcs.subject_type as combination_subject_type,
        CASE
          WHEN s.subject_type = 'compulsory' OR alcs.subject_type = 'principal' THEN TRUE
          ELSE FALSE
        END as is_compulsory
      FROM a_level_combination_subjects alcs
      JOIN a_level_subjects s ON alcs.subject_id = s.id
      WHERE alcs.combination_id = ?
      ORDER BY
        CASE WHEN s.subject_type = 'compulsory' THEN 1
             WHEN alcs.subject_type = 'principal' THEN 2
             ELSE 3 END,
        s.name
    `;

    const subjectsResult = await executeQuery(subjectsQuery, [id]);

    if (!subjectsResult.success) {
      throw new Error(subjectsResult.error);
    }

    // Also get the subsidiary subject for this combination
    const subsidiaryQuery = `
      SELECT
        s.id, s.name, s.short_name, s.uneb_code, s.subject_type,
        'subsidiary' as combination_subject_type,
        TRUE as is_compulsory
      FROM a_level_combinations alc
      JOIN a_level_subjects s ON alc.subsidiary_subject_id = s.id
      WHERE alc.id = ?
    `;

    const subsidiaryResult = await executeQuery(subsidiaryQuery, [id]);

    let allSubjects = subjectsResult.data || [];

    // Add subsidiary subject if it exists
    if (subsidiaryResult.success && subsidiaryResult.data.length > 0) {
      allSubjects.push(subsidiaryResult.data[0]);
    }

    res.json({
      success: true,
      data: allSubjects
    });

  } catch (error) {
    console.error('Get combination subjects error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve combination subjects'
    });
  }
});

module.exports = router;
