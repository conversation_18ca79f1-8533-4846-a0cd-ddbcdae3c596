// AIMS Layout System
// Simple, responsive layout components

const Layout = {
  // Layout state
  state: {
    sidebarCollapsed: false,
    currentPage: 'dashboard',
    currentUser: null,
    currentAcademicYear: null,
    currentTerm: null,

  },

  // Initialize the layout system
  init() {
    this.addLayoutStyles();
    this.createLayout();
    this.initializeEventListeners();
    this.initializeNavigation();
    this.updateAcademicInfo();
  },

  // Add layout-specific styles
  addLayoutStyles() {
    if (document.getElementById('layout-styles')) return;

    const style = document.createElement('style');
    style.id = 'layout-styles';
    style.textContent = `
      /* Ensure proper layout structure */
      html, body {
        height: 100%;
        overflow: hidden;
      }

      #app-layout {
        height: 100vh;
        overflow: hidden;
      }

      /* Sidebar styles */
      #sidebar {
        transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      #sidebar.sidebar-collapsed {
        width: 4rem !important;
      }

      /* Logo styling */
      .sidebar-logo {
        transition: all 0.3s ease;
      }

      #sidebar.sidebar-collapsed .sidebar-logo {
        width: 2rem;
        height: 2rem;
      }

      /* Content area scrolling */
      #content-area {
        height: calc(100vh - 7rem); /* Subtract header (4rem) + footer (3rem) */
        overflow-y: auto;
        overflow-x: hidden;
      }

      /* Custom scrollbar for content area */
      #content-area::-webkit-scrollbar {
        width: 6px;
      }

      #content-area::-webkit-scrollbar-track {
        background: #f1f5f9;
      }

      #content-area::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 3px;
      }

      #content-area::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
      }

      /* Navigation menu scrolling */
      nav {
        overflow-y: auto;
        overflow-x: hidden;
      }

      nav::-webkit-scrollbar {
        width: 4px;
      }

      nav::-webkit-scrollbar-track {
        background: transparent;
      }

      nav::-webkit-scrollbar-thumb {
        background: #e2e8f0;
        border-radius: 2px;
      }

      nav::-webkit-scrollbar-thumb:hover {
        background: #cbd5e1;
      }

      /* Custom scrollbar for modals (same as sidebar nav) */
      .custom-scrollbar::-webkit-scrollbar {
        width: 4px;
      }

      .custom-scrollbar::-webkit-scrollbar-track {
        background: transparent;
      }

      .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #e2e8f0;
        border-radius: 2px;
      }

      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #cbd5e1;
      }
    `;
    document.head.appendChild(style);
  },

  // Create the main layout structure
  createLayout() {
    const body = document.body;
    body.innerHTML = `
      <!-- App Layout -->
      <div id="app-layout" class="h-screen bg-gray-50 flex overflow-hidden">
        <!-- Sidebar -->
        ${this.renderSidebar()}

        <!-- Main Content -->
        <div id="main-content" class="flex-1 flex flex-col transition-all duration-300 ease-in-out min-w-0">
          <!-- Top Navigation -->
          ${this.renderTopNavbar()}

          <!-- Content Area -->
          <main id="content-area" class="flex-1 p-6 overflow-y-auto overflow-x-hidden bg-gray-50">
            <!-- Content will be loaded here -->
          </main>

          <!-- Footer -->
          ${this.renderFooter()}
        </div>
      </div>

      <!-- Modal Container -->
      <div id="modal-container" class="fixed inset-0 z-50 hidden"></div>
    `;
  },

  // Render sidebar
  renderSidebar() {
    return `
      <aside id="sidebar" class="w-72 h-screen bg-white border-r border-gray-200 flex flex-col transition-all duration-300 ease-in-out shadow-lg overflow-hidden flex-shrink-0">
        <!-- Sidebar Header -->
        <div class="h-16 flex items-center justify-between px-6 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-indigo-600 flex-shrink-0">
          <div class="flex items-center space-x-3 min-w-0">
            <div class="sidebar-logo w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center flex-shrink-0 p-1">
              <img src="../assets/images/logo.png" alt="AIMS Logo" class="w-full h-full object-contain">
            </div>
            <div class="text-white sidebar-text transition-opacity duration-300">
              <div class="font-bold text-lg whitespace-nowrap">AIMS</div>
              <div class="text-xs opacity-90 whitespace-nowrap">Report Cards</div>
            </div>
          </div>
          <button id="sidebar-toggle" class="p-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors flex-shrink-0">
            <i class="fas fa-chevron-left text-white"></i>
          </button>
        </div>

        <!-- Navigation Menu -->
        <nav class="flex-1 overflow-y-auto overflow-x-hidden py-4">
          <div id="navigation-menu" class="px-3 space-y-1">
            <!-- Navigation items will be loaded here -->
          </div>
        </nav>
      </aside>
    `;
  },

  // Render top navbar
  renderTopNavbar() {
    return `
      <header class="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6 shadow-sm flex-shrink-0">
        <!-- Left Section - Empty for cleaner look -->
        <div class="flex items-center space-x-4">
        </div>

        <!-- Center Section - Academic Info -->
        <div class="hidden md:flex items-center space-x-6 bg-gray-50 px-4 py-2 rounded-lg">
          <div class="flex items-center space-x-2">
            <i class="fas fa-graduation-cap text-indigo-600"></i>
            <div class="text-sm">
              <div class="font-medium text-gray-900" id="academic-period">Loading...</div>
            </div>
          </div>
        </div>
        
        <!-- Right Section - User Menu -->
        <div class="flex items-center space-x-4">
          <!-- User Menu -->
          <div class="relative">
            <button id="user-menu-button" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors">
              <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center">
                <i class="fas fa-user text-white text-sm"></i>
              </div>
              <div class="hidden sm:block text-left">
                <div class="text-sm font-medium text-gray-900" id="user-display-name">Admin</div>
                <div class="text-xs text-gray-500">System Administrator</div>
              </div>
              <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
            </button>
            
            <!-- User Dropdown -->
            <div id="user-dropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 hidden">
              <div class="py-2">
                <a href="#" onclick="showProfileModal()" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  <i class="fas fa-user-cog mr-3"></i>
                  Profile Settings
                </a>
                <hr class="my-2">
                <a href="#" onclick="logout()" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                  <i class="fas fa-sign-out-alt mr-3"></i>
                  Logout
                </a>
              </div>
            </div>
          </div>
        </div>
      </header>
    `;
  },

  // Render modern footer
  renderFooter() {
    return `
      <footer class="h-12 bg-white border-t border-gray-200 flex items-center justify-center px-6 flex-shrink-0">
        <div class="text-sm text-gray-500">
          © 2025 AIMS - Report Card Generation System for Secondary Schools. All rights reserved.
        </div>
      </footer>
    `;
  },

  // Initialize event listeners
  initializeEventListeners() {
    console.log('🔧 Initializing Layout event listeners...');

    // Sidebar toggle
    const sidebarToggle = document.getElementById('sidebar-toggle');
    if (sidebarToggle) {
      sidebarToggle.addEventListener('click', () => {
        this.toggleSidebar();
      });
      console.log('✅ Sidebar toggle listener added');
    }

    // User menu toggle
    const userMenuButton = document.getElementById('user-menu-button');
    if (userMenuButton) {
      userMenuButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.toggleUserMenu();
        console.log('👤 User menu toggled');
      });
      console.log('✅ User menu toggle listener added');
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', (e) => {
      const userDropdown = document.getElementById('user-dropdown');
      if (userDropdown && !e.target.closest('#user-menu-button') && !userDropdown.contains(e.target)) {
        userDropdown.classList.add('hidden');
      }
    });


    console.log('✅ Layout event listeners initialized');
  },

  // Initialize navigation system
  initializeNavigation() {
    console.log('🧭 Initializing navigation system...');

    // Wait a bit for the DOM to be ready, then initialize navigation
    setTimeout(() => {
      if (window.ModernNavigation) {
        console.log('✅ Navigation found, initializing...');
        window.ModernNavigation.init();
      } else {
        console.warn('⚠️ Navigation not found, navigation may not work properly');
      }
    }, 100);
  },

  // Toggle sidebar
  toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const toggleIcon = document.querySelector('#sidebar-toggle i');
    const sidebarTexts = document.querySelectorAll('.sidebar-text');

    this.state.sidebarCollapsed = !this.state.sidebarCollapsed;

    if (this.state.sidebarCollapsed) {
      // Collapse sidebar
      sidebar.classList.remove('w-72');
      sidebar.classList.add('w-16');
      sidebar.classList.add('sidebar-collapsed');
      toggleIcon.classList.remove('fa-chevron-left');
      toggleIcon.classList.add('fa-chevron-right');

      // Hide text elements
      sidebarTexts.forEach(text => {
        text.style.opacity = '0';
        text.style.visibility = 'hidden';
      });
    } else {
      // Expand sidebar
      sidebar.classList.remove('w-16');
      sidebar.classList.add('w-72');
      sidebar.classList.remove('sidebar-collapsed');
      toggleIcon.classList.remove('fa-chevron-right');
      toggleIcon.classList.add('fa-chevron-left');

      // Show text elements
      sidebarTexts.forEach(text => {
        text.style.opacity = '1';
        text.style.visibility = 'visible';
      });
    }

    // Notify navigation component about state change
    if (window.ModernNavigation) {
      window.ModernNavigation.handleSidebarToggle(this.state.sidebarCollapsed);
    }
  },

  // Toggle user menu
  toggleUserMenu() {
    const dropdown = document.getElementById('user-dropdown');
    if (dropdown) {
      dropdown.classList.toggle('hidden');
      console.log('👤 User dropdown toggled:', !dropdown.classList.contains('hidden') ? 'shown' : 'hidden');
    } else {
      console.error('❌ User dropdown element not found');
    }
  },

  // Update academic information (always fresh from database)
  async updateAcademicInfo() {
    const academicPeriod = document.getElementById('academic-period');
    if (!academicPeriod) return;

    try {
      console.log('📅 Fetching fresh academic info from database...');

      // Use the global API service
      const result = await window.AcademicAPI.getCurrentContext();

      if (result.success) {
        const data = result.data;

        console.log('📅 Academic Context Response:', data);

        if (data.setupRequired) {
          this.displayAcademicInfo({
            text: 'Academic Setup Required',
            className: 'text-yellow-600'
          });
        } else if (data.academicYear && data.currentTerm) {
          const yearText = data.academicYear.name || data.academicYear.year || 'Unknown Year';
          const termText = data.currentTerm.name || 'Unknown Term';
          this.displayAcademicInfo({
            text: `${yearText} - ${termText}`,
            className: 'text-gray-900'
          });
        } else {
          this.displayAcademicInfo({
            text: 'No Active Academic Period',
            className: 'text-yellow-600'
          });
        }
      } else {
        this.displayAcademicInfo({
          text: 'Server Connection Error',
          className: 'text-yellow-600'
        });
      }
    } catch (error) {
      console.error('❌ Error fetching academic info:', error);
      this.displayAcademicInfo({
        text: 'Academic Setup Required',
        className: 'text-yellow-600'
      });
    }
  },

  // Display academic info in the navbar
  displayAcademicInfo(data) {
    const academicPeriod = document.getElementById('academic-period');
    if (!academicPeriod) return;

    academicPeriod.textContent = data.text;
    academicPeriod.className = `font-medium ${data.className}`;
  },



  // Clock functionality removed since date display was removed from navbar

  // Load page content (deprecated - use PageRouter.loadPage instead)
  loadPage(page) {
    console.warn('Layout.loadPage is deprecated. Use PageRouter.loadPage instead.');
    if (window.PageRouter && window.PageRouter.state.currentPage !== page) {
      window.PageRouter.loadPage(page);
    }
  }
};

// Export to global scope
window.Layout = Layout;
