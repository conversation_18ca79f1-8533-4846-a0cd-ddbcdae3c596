// AIMS Environment Configuration Manager
// Centralized configuration management for the Electron desktop application

class EnvironmentConfig {
  constructor() {
    this.config = this.loadConfiguration();
  }

  // Load configuration from environment variables
  loadConfiguration() {
    return {
      // Server Configuration (Electron + Express + XAMPP)
      api: {
        baseUrl: this.getEnvVar('API_BASE_URL', 'http://localhost:3001/api'),
        serverUrl: this.getEnvVar('SERVER_URL', 'http://localhost:3001'),
        timeout: 30000 // 30 seconds
      },

      // Development Configuration
      development: {
        debugMode: this.getBooleanEnvVar('DEBUG_MODE', true), // Enable debug by default
        logLevel: this.getEnvVar('LOG_LEVEL', 'debug')
      }
    };
  }

  // Get environment variable with fallback
  getEnvVar(key, defaultValue = '') {
    // For Electron desktop application, check multiple sources

    // 1. Check Node.js process environment (Electron main/renderer)
    if (typeof process !== 'undefined' && process.env) {
      return process.env[key] || defaultValue;
    }

    // 2. Check window object for Electron renderer process
    if (typeof window !== 'undefined' && window.process && window.process.env) {
      return window.process.env[key] || defaultValue;
    }

    // 3. Check global AIMS configuration
    if (typeof window !== 'undefined' && window.AIMS && window.AIMS.config) {
      return window.AIMS.config[key] || defaultValue;
    }

    return defaultValue;
  }

  // Get boolean environment variable
  getBooleanEnvVar(key, defaultValue = false) {
    const value = this.getEnvVar(key, defaultValue.toString());
    return value === 'true' || value === '1' || value === 'yes';
  }

  // Get API endpoint URL
  getApiUrl(endpoint = '') {
    const baseUrl = this.config.api.baseUrl.replace(/\/$/, ''); // Remove trailing slash
    const cleanEndpoint = endpoint.replace(/^\//, ''); // Remove leading slash
    return cleanEndpoint ? `${baseUrl}/${cleanEndpoint}` : baseUrl;
  }

  // Get full server URL
  getServerUrl(path = '') {
    const serverUrl = this.config.api.serverUrl.replace(/\/$/, ''); // Remove trailing slash
    const cleanPath = path.replace(/^\//, ''); // Remove leading slash
    return cleanPath ? `${serverUrl}/${cleanPath}` : serverUrl;
  }



  // Get configuration value by path
  get(path, defaultValue = null) {
    const keys = path.split('.');
    let value = this.config;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return defaultValue;
      }
    }
    
    return value;
  }

  // Log configuration (for debugging)
  logConfiguration() {
    if (this.config.development.debugMode) {
      console.group('AIMS Configuration');
      console.log('API Base URL:', this.config.api.baseUrl);
      console.log('Server URL:', this.config.api.serverUrl);
      console.log('Debug Mode:', this.config.development.debugMode);
      console.groupEnd();
    }
  }
}

// Create and export singleton instance
const Config = new EnvironmentConfig();

// Log configuration in development mode
Config.logConfiguration();

// Make it available globally
if (typeof window !== 'undefined') {
  window.AIMSConfig = Config;
}

console.log('✅ AIMS Configuration loaded and available globally');
