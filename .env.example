# AIMS Environment Configuration
# Electron Desktop Application with XAMPP Database

# Database Configuration (XAMPP MySQL)
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=aims_db

# Server Configuration (Express Backend)
# API Base URL - Where your Electron frontend sends API requests
API_BASE_URL=http://localhost:3001/api
# Server URL - Your Express server base URL
SERVER_URL=http://localhost:3001
# Server Port - Port where Express server runs
SERVER_PORT=3001
# Environment - Development mode for debugging
NODE_ENV=development


