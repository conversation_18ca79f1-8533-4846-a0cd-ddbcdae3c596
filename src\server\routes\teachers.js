const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');
const { BusinessValidation } = require('../utils/validation');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get all teachers with optional filters
router.get('/', async (req, res) => {
  try {
    const { teacher_type, employment_status, academic_year_id } = req.query;
    
    let query = `
      SELECT
        t.id, t.first_name, t.middle_name, t.last_name, t.initials,
        t.teaching_subjects, t.teacher_type, t.employment_status,
        t.joining_date, t.profile_picture, t.academic_year_id,
        t.created_by_id, t.updated_by_id, t.created_at, t.updated_at
      FROM teachers t
      WHERE 1=1
    `;
    
    let params = [];
    
    if (teacher_type) {
      query += ' AND t.teacher_type = ?';
      params.push(teacher_type);
    }
    
    if (employment_status) {
      query += ' AND t.employment_status = ?';
      params.push(employment_status);
    }
    
    if (academic_year_id) {
      // Filter teachers who have assignments in the specified academic year
      query += ` AND t.id IN (
        SELECT DISTINCT teacher_id FROM class_subject_assignments
        WHERE teacher_id IS NOT NULL
      )`;
      // Note: academic_year_id filtering removed as class_subject_assignments doesn't have this field
    }
    
    query += ' ORDER BY t.last_name, t.first_name';
    
    const result = await executeQuery(query, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get teachers error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve teachers'
    });
  }
});

// Get teacher by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        t.*,
        GROUP_CONCAT(
          DISTINCT CONCAT(c.name, ' - ', COALESCE(os.name, as_sub.name)) SEPARATOR ', '
        ) as assigned_subjects
      FROM teachers t
      LEFT JOIN class_subject_assignments csa ON t.id = csa.teacher_id
      LEFT JOIN classes c ON csa.class_id = c.id
      LEFT JOIN o_level_subjects os ON csa.subject_id = os.id AND csa.subject_level = 'o_level'
      LEFT JOIN a_level_subjects as_sub ON csa.subject_id = as_sub.id AND csa.subject_level = 'a_level'
      WHERE t.id = ?
      GROUP BY t.id
    `;
    
    const result = await executeQuery(query, [id]);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get teacher error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve teacher'
    });
  }
});

// Create new teacher
router.post('/', async (req, res) => {
  try {
    const {
      first_name, middle_name, last_name, initials, teaching_subjects,
      teacher_type, joining_date, employment_status, profile_picture, academic_year_id
    } = req.body;

    // Get current user ID for audit fields
    const userId = req.user?.id || 1; // Default to system admin if no user context

    // Add audit fields to data
    const teacherData = {
      first_name, middle_name, last_name, initials, teaching_subjects,
      teacher_type, joining_date, employment_status, profile_picture, academic_year_id,
      created_by_id: userId
    };

    // Validate using business logic
    const validationErrors = await BusinessValidation.validateTeacher(teacherData, false);
    const auditErrors = BusinessValidation.validateAuditFields(teacherData, false);
    const allErrors = [...validationErrors, ...auditErrors];

    if (allErrors.length > 0) {
      return res.status(400).json(BusinessValidation.formatValidationErrors(allErrors));
    }

    // Insert new teacher
    const insertQuery = `
      INSERT INTO teachers (
        first_name, middle_name, last_name, initials, teaching_subjects,
        teacher_type, joining_date, employment_status, profile_picture,
        academic_year_id, created_by_id, updated_by_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, NOW(), NOW())
    `;

    const insertResult = await executeQuery(insertQuery, [
      first_name, middle_name, last_name, initials, teaching_subjects,
      teacher_type, joining_date, employment_status, profile_picture,
      academic_year_id, userId
    ]);

    if (!insertResult.success) {
      throw new Error(insertResult.error);
    }

    // Get the created teacher
    const newTeacherQuery = 'SELECT * FROM teachers WHERE id = ?';
    const newTeacherResult = await executeQuery(newTeacherQuery, [insertResult.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'Teacher created successfully',
      data: newTeacherResult.data[0]
    });

  } catch (error) {
    console.error('Create teacher error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create teacher'
    });
  }
});

// Update teacher
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      first_name, middle_name, last_name, initials, teaching_subjects,
      teacher_type, joining_date, employment_status, profile_picture, academic_year_id
    } = req.body;

    // Get current user ID for audit fields
    const userId = req.user?.id || 1; // Default to system admin if no user context

    // Check if teacher exists
    const checkQuery = 'SELECT id FROM teachers WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    // Add audit fields to data
    const teacherData = {
      first_name, middle_name, last_name, initials, teaching_subjects,
      teacher_type, joining_date, employment_status, profile_picture, academic_year_id,
      updated_by_id: userId
    };

    // Validate using business logic
    const validationErrors = await BusinessValidation.validateTeacher(teacherData, true, id);
    const auditErrors = BusinessValidation.validateAuditFields(teacherData, true);
    const allErrors = [...validationErrors, ...auditErrors];

    if (allErrors.length > 0) {
      return res.status(400).json(BusinessValidation.formatValidationErrors(allErrors));
    }

    // Update teacher
    const updateQuery = `
      UPDATE teachers SET
        first_name = ?, middle_name = ?, last_name = ?, initials = ?, teaching_subjects = ?,
        teacher_type = ?, joining_date = ?, employment_status = ?, profile_picture = ?,
        academic_year_id = ?, updated_by_id = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const updateResult = await executeQuery(updateQuery, [
      first_name, middle_name, last_name, initials, teaching_subjects,
      teacher_type, joining_date, employment_status, profile_picture,
      academic_year_id, userId, id
    ]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    // Get updated teacher
    const updatedTeacherQuery = 'SELECT * FROM teachers WHERE id = ?';
    const updatedTeacherResult = await executeQuery(updatedTeacherQuery, [id]);

    res.json({
      success: true,
      message: 'Teacher updated successfully',
      data: updatedTeacherResult.data[0]
    });

  } catch (error) {
    console.error('Update teacher error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update teacher'
    });
  }
});

// Delete teacher
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if teacher exists
    const checkQuery = 'SELECT id FROM teachers WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    // Check if teacher has any assignments
    const assignmentQuery = 'SELECT id FROM class_subject_assignments WHERE teacher_id = ? LIMIT 1';
    const assignmentResult = await executeQuery(assignmentQuery, [id]);

    if (assignmentResult.success && assignmentResult.data.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete teacher with existing assignments. Please remove assignments first.'
      });
    }

    // Delete teacher
    const deleteQuery = 'DELETE FROM teachers WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'Teacher deleted successfully'
    });

  } catch (error) {
    console.error('Delete teacher error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete teacher'
    });
  }
});

// Get teacher statistics
router.get('/stats/overview', async (req, res) => {
  try {
    const statsQuery = `
      SELECT
        COUNT(*) as total_teachers,
        COUNT(CASE WHEN teacher_type = 'Class Teacher' THEN 1 END) as class_teachers,
        COUNT(CASE WHEN teacher_type = 'Subject Teacher' THEN 1 END) as subject_teachers,
        COUNT(CASE WHEN employment_status = 'active' THEN 1 END) as active_teachers,
        COUNT(CASE WHEN employment_status = 'inactive' THEN 1 END) as inactive_teachers
      FROM teachers
    `;

    const result = await executeQuery(statsQuery);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get teacher stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve teacher statistics'
    });
  }
});

// Get registration form data (for dropdowns and form setup)
router.get('/registration-form-data', async (req, res) => {
  try {
    // Get O-Level and A-Level subjects for teacher specialization
    const oLevelSubjectsQuery = `
      SELECT id, name, short_name, 'o_level' as level, subject_type
      FROM o_level_subjects
      WHERE is_active = TRUE
      ORDER BY name
    `;

    const aLevelSubjectsQuery = `
      SELECT id, name, short_name, 'a_level' as level, subject_type
      FROM a_level_subjects
      WHERE is_active = TRUE
      ORDER BY name
    `;

    const [oLevelResult, aLevelResult] = await Promise.all([
      executeQuery(oLevelSubjectsQuery),
      executeQuery(aLevelSubjectsQuery)
    ]);

    if (!oLevelResult.success || !aLevelResult.success) {
      throw new Error('Failed to fetch subjects');
    }

    // Combine subjects
    const allSubjects = [
      ...(oLevelResult.data || []),
      ...(aLevelResult.data || [])
    ];

    // Prepare form data
    const formData = {
      subjects: allSubjects,
      teacher_types: [
        { value: 'Class Teacher', label: 'Class Teacher' },
        { value: 'Subject Teacher', label: 'Subject Teacher' }
      ],
      employment_statuses: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' }
      ],
      qualifications: [
        { value: 'certificate', label: 'Certificate' },
        { value: 'diploma', label: 'Diploma' },
        { value: 'degree', label: 'Bachelor\'s Degree' },
        { value: 'masters', label: 'Master\'s Degree' },
        { value: 'phd', label: 'PhD' }
      ]
    };

    res.json({
      success: true,
      data: formData
    });

  } catch (error) {
    console.error('Get registration form data error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve registration form data'
    });
  }
});

// Get teacher assignments
router.get('/:id/assignments', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        csa.*,
        c.name as class_name,
        COALESCE(os.name, as_sub.name) as subject_name,
        COALESCE(os.short_name, as_sub.short_name) as subject_short_name,
        cl.name as class_level_name,
        el.name as education_level_name
      FROM class_subject_assignments csa
      JOIN classes c ON csa.class_id = c.id
      LEFT JOIN o_level_subjects os ON csa.subject_id = os.id AND csa.subject_level = 'o_level'
      LEFT JOIN a_level_subjects as_sub ON csa.subject_id = as_sub.id AND csa.subject_level = 'a_level'
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      WHERE csa.teacher_id = ?
      ORDER BY cl.sort_order, c.name, COALESCE(os.name, as_sub.name)
    `;

    const result = await executeQuery(query, [id]);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get teacher assignments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve teacher assignments'
    });
  }
});

module.exports = router;
