/**
 * Test file for AuditFieldsUtil
 * This file contains tests to verify the audit fields utility is working correctly
 */

const AuditFieldsTest = {
  /**
   * Run all tests for the audit fields utility
   */
  runTests() {
    console.log('🧪 Running AuditFieldsUtil tests...');
    
    try {
      this.testGetCurrentUserId();
      this.testAddAuditFieldsToData();
      this.testCreateAuditFieldsHTML();
      this.testFormatAuditInfo();
      this.testValidateAuditFields();
      
      console.log('✅ All AuditFieldsUtil tests passed!');
      return true;
    } catch (error) {
      console.error('❌ AuditFieldsUtil tests failed:', error);
      return false;
    }
  },

  /**
   * Test getCurrentUserId method
   */
  testGetCurrentUserId() {
    console.log('Testing getCurrentUserId...');
    
    if (!window.AuditFieldsUtil) {
      throw new Error('AuditFieldsUtil not found');
    }

    const userId = window.AuditFieldsUtil.getCurrentUserId();
    
    if (typeof userId !== 'number' || userId < 1) {
      throw new Error(`Expected valid user ID, got: ${userId}`);
    }
    
    console.log(`✓ getCurrentUserId returned: ${userId}`);
  },

  /**
   * Test addAuditFieldsToData method
   */
  testAddAuditFieldsToData() {
    console.log('Testing addAuditFieldsToData...');
    
    // Test for new record
    let data = { name: 'Test Record' };
    let result = window.AuditFieldsUtil.addAuditFieldsToData(data, false);
    
    if (!result.created_by_id) {
      throw new Error('created_by_id not added for new record');
    }
    
    if (result.updated_by_id) {
      throw new Error('updated_by_id should not be set for new record');
    }
    
    // Test for update
    data = { name: 'Updated Record' };
    result = window.AuditFieldsUtil.addAuditFieldsToData(data, true);
    
    if (!result.updated_by_id) {
      throw new Error('updated_by_id not added for update');
    }
    
    console.log('✓ addAuditFieldsToData works correctly');
  },

  /**
   * Test createAuditFieldsHTML method
   */
  testCreateAuditFieldsHTML() {
    console.log('Testing createAuditFieldsHTML...');
    
    const html = window.AuditFieldsUtil.createAuditFieldsHTML(true);
    
    if (!html.includes('name="created_by_id"')) {
      throw new Error('created_by_id field not found in HTML');
    }
    
    if (!html.includes('name="updated_by_id"')) {
      throw new Error('updated_by_id field not found in HTML');
    }
    
    if (!html.includes('type="hidden"')) {
      throw new Error('Hidden input type not found in HTML');
    }
    
    console.log('✓ createAuditFieldsHTML generates correct HTML');
  },

  /**
   * Test formatAuditInfo method
   */
  testFormatAuditInfo() {
    console.log('Testing formatAuditInfo...');
    
    const record = {
      created_by_id: 1,
      created_by_name: 'Admin User',
      created_at: '2024-01-01T10:00:00Z',
      updated_by_id: 2,
      updated_by_name: 'Another Admin',
      updated_at: '2024-01-02T15:30:00Z'
    };
    
    const auditInfo = window.AuditFieldsUtil.formatAuditInfo(record);
    
    if (!auditInfo.created || !auditInfo.updated) {
      throw new Error('Audit info structure is incorrect');
    }
    
    if (auditInfo.created.by !== 'Admin User') {
      throw new Error('Created by name not formatted correctly');
    }
    
    if (auditInfo.updated.by !== 'Another Admin') {
      throw new Error('Updated by name not formatted correctly');
    }
    
    console.log('✓ formatAuditInfo formats data correctly');
  },

  /**
   * Test validateAuditFields method
   */
  testValidateAuditFields() {
    console.log('Testing validateAuditFields...');
    
    // Test valid new record
    let data = { created_by_id: 1 };
    let isValid = window.AuditFieldsUtil.validateAuditFields(data, false);
    
    if (!isValid) {
      throw new Error('Valid new record failed validation');
    }
    
    // Test invalid new record
    data = {};
    isValid = window.AuditFieldsUtil.validateAuditFields(data, false);
    
    if (isValid) {
      throw new Error('Invalid new record passed validation');
    }
    
    // Test valid update
    data = { updated_by_id: 1 };
    isValid = window.AuditFieldsUtil.validateAuditFields(data, true);
    
    if (!isValid) {
      throw new Error('Valid update failed validation');
    }
    
    // Test invalid update
    data = {};
    isValid = window.AuditFieldsUtil.validateAuditFields(data, true);
    
    if (isValid) {
      throw new Error('Invalid update passed validation');
    }
    
    console.log('✓ validateAuditFields validates correctly');
  },

  /**
   * Test integration with a mock form
   */
  testFormIntegration() {
    console.log('Testing form integration...');
    
    // Create a mock form
    const form = document.createElement('form');
    form.id = 'test-form';
    form.innerHTML = window.AuditFieldsUtil.createAuditFieldsHTML(true);
    document.body.appendChild(form);
    
    try {
      // Test populating fields
      window.AuditFieldsUtil.populateAuditFields('test-form', false);
      
      const createdByField = form.querySelector('input[name="created_by_id"]');
      if (!createdByField || !createdByField.value) {
        throw new Error('created_by_id field not populated');
      }
      
      console.log('✓ Form integration works correctly');
    } finally {
      // Clean up
      document.body.removeChild(form);
    }
  }
};

// Auto-run tests when DOM is loaded (only in development)
document.addEventListener('DOMContentLoaded', () => {
  // Only run tests if in development mode and AuditFieldsUtil is available
  if (window.AIMSConfig && window.AIMSConfig.get && window.AIMSConfig.get('development.debugMode')) {
    if (window.AuditFieldsUtil) {
      setTimeout(() => {
        AuditFieldsTest.runTests();
      }, 1000); // Wait for other components to load
    }
  }
});

// Make it globally available for manual testing
window.AuditFieldsTest = AuditFieldsTest;
