const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const { testConnection, getDatabaseStats } = require('../database/connection');
const { initializeDatabase, isDatabaseInitialized } = require('../database/init');

// Import routes
const authRoutes = require('./routes/auth');
const systemUsersRoutes = require('./routes/system-users');
const academicRoutes = require('./routes/academic');
const studentRoutes = require('./routes/students');
const teacherRoutes = require('./routes/teachers');
const assessmentRoutes = require('./routes/assessments');
const attendanceRoutes = require('./routes/attendance');
const reportRoutes = require('./routes/reports');
const settingsRoutes = require('./routes/settings');
const aLevelCombinationsRoutes = require('./routes/a-level-combinations');
const promotionsRoutes = require('./routes/promotions');

const app = express();
const PORT = process.env.SERVER_PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static(path.join(__dirname, '../../uploads')));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    let dbConnected = false;
    let stats = { success: false, data: null };

    try {
      dbConnected = await testConnection();
      if (dbConnected) {
        stats = await getDatabaseStats();
      }
    } catch (error) {
      console.log('Health check: Database not ready yet:', error.message);
    }

    res.json({
      status: dbConnected ? 'OK' : 'INITIALIZING',
      timestamp: new Date().toISOString(),
      database: {
        connected: dbConnected,
        stats: stats.success ? stats.data : null
      },
      server: {
        port: PORT,
        environment: process.env.NODE_ENV || 'development'
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      message: error.message
    });
  }
});

// Database initialization endpoint
app.post('/api/init-database', async (req, res) => {
  try {
    const isInitialized = await isDatabaseInitialized();
    
    if (isInitialized) {
      return res.json({
        success: true,
        message: 'Database is already initialized'
      });
    }

    const result = await initializeDatabase();
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/system-users', systemUsersRoutes);
app.use('/api/academic', academicRoutes);
app.use('/api/dashboard', require('./routes/dashboard'));
app.use('/api/students', studentRoutes);
app.use('/api/classes-streams', require('./routes/classes-streams'));
app.use('/api/teachers', teacherRoutes);
app.use('/api/assessments', assessmentRoutes);
app.use('/api/attendance', attendanceRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/a-level-combinations', aLevelCombinationsRoutes);
app.use('/api/promotions', promotionsRoutes);

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server Error:', error);
  
  res.status(error.status || 500).json({
    success: false,
    message: error.message || 'Internal Server Error',
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found'
  });
});

// Start server
async function startServer() {
  try {
    console.log('🚀 Starting AIMS Server...');
    console.log('📊 Academic Information Management System');
    console.log('🏫 Nyabikoni Secondary School');
    console.log('');

    // First, try to initialize database (this will create the database if it doesn't exist)
    console.log('🔌 Checking database setup...');

    // Check if database is initialized
    let isInitialized = false;
    try {
      isInitialized = await isDatabaseInitialized();
    } catch (error) {
      // Database might not exist, that's okay - we'll create it
      console.log('📊 Database not found, will create it...');
      isInitialized = false;
    }

    if (!isInitialized) {
      console.log('🚀 Initializing database...');
      const initResult = await initializeDatabase();

      if (!initResult.success) {
        console.error('❌ Database initialization failed:', initResult.error);
        process.exit(1);
      }
      console.log('✅ Database initialized successfully!');
    }

    // Now test the connection to the initialized database
    console.log('🔌 Testing database connection...');
    const dbConnected = await testConnection();

    if (!dbConnected) {
      console.error('❌ Database connection failed after initialization. Please check your database configuration.');
      process.exit(1);
    }

    console.log('✅ Database connection verified!');

    // Start the server
    app.listen(PORT, () => {
      console.log('🚀 AIMS Server started successfully!');
      console.log(`📡 Server running on http://localhost:${PORT}`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📊 Health check: http://localhost:${PORT}/health`);
      console.log('✅ Ready to accept connections');
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM. Shutting down gracefully...');
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server if this file is run directly
if (require.main === module) {
  startServer();
}

module.exports = app;
