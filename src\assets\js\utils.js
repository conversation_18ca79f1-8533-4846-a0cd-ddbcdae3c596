// AIMS - Utility Functions
// Common utility functions used throughout the application

// Date formatting utilities
const DateUtils = {
  // Format date to YYYY-MM-DD
  formatDate(date) {
    if (!date) return '';
    const d = new Date(date);
    return d.toISOString().split('T')[0];
  },
  
  // Format date to readable format
  formatDateReadable(date) {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('en-UG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  },
  
  // Get current academic year
  getCurrentAcademicYear() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1; // JavaScript months are 0-indexed
    
    // Academic year starts in February in Uganda
    if (month >= 2) {
      return year;
    } else {
      return year - 1;
    }
  },
  
  // Get current term based on date
  getCurrentTerm() {
    const now = new Date();
    const month = now.getMonth() + 1;
    
    if (month >= 2 && month <= 5) {
      return 1; // Term 1: February - May
    } else if (month >= 6 && month <= 9) {
      return 2; // Term 2: June - September
    } else {
      return 3; // Term 3: October - January
    }
  }
};

// String utilities
const StringUtils = {
  // Capitalize first letter
  capitalize(str) {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  },
  
  // Convert to title case
  toTitleCase(str) {
    if (!str) return '';
    return str.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  },
  
  // Generate random ID
  generateId(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
  
  // Truncate text
  truncate(str, length = 50) {
    if (!str) return '';
    if (str.length <= length) return str;
    return str.substring(0, length) + '...';
  }
};

// Validation utilities
const ValidationUtils = {
  // Validate email
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },
  
  // Validate phone number (Uganda format)
  isValidPhone(phone) {
    const phoneRegex = /^(\+256|0)[7-9]\d{8}$/;
    return phoneRegex.test(phone);
  },
  
  // Validate national ID (Uganda format)
  isValidNationalId(id) {
    const idRegex = /^[A-Z]{2}\d{7}[A-Z]$/;
    return idRegex.test(id);
  },
  
  // Validate required field
  isRequired(value) {
    return value !== null && value !== undefined && value.toString().trim() !== '';
  },
  
  // Validate grade (0-3 for CA, 0-100 for exams)
  isValidGrade(grade, type = 'exam') {
    const num = parseFloat(grade);
    if (isNaN(num)) return false;
    
    if (type === 'ca') {
      return num >= 0 && num <= 3;
    } else {
      return num >= 0 && num <= 100;
    }
  }
};

// Grade calculation utilities
const GradeUtils = {
  // Calculate final grade (CA 20% + Exam 80%)
  calculateFinalGrade(caScore, examScore) {
    if (caScore === null || caScore === undefined || examScore === null || examScore === undefined) {
      return null;
    }
    
    // Convert CA score (0-3) to percentage
    const caPercentage = (caScore / 3) * 100;
    
    // Calculate weighted average
    const finalScore = (caPercentage * 0.2) + (examScore * 0.8);
    
    return Math.round(finalScore * 100) / 100; // Round to 2 decimal places
  },
  
  // Get letter grade from percentage
  getLetterGrade(percentage) {
    if (percentage === null || percentage === undefined) return 'N/A';
    
    if (percentage >= 80) return 'A';
    if (percentage >= 70) return 'B';
    if (percentage >= 60) return 'C';
    if (percentage >= 50) return 'D';
    return 'E';
  },
  
  // Get grade description
  getGradeDescription(letterGrade) {
    const descriptions = {
      'A': 'Excellent',
      'B': 'Very Good',
      'C': 'Good',
      'D': 'Satisfactory',
      'E': 'Unsatisfactory'
    };
    return descriptions[letterGrade] || 'N/A';
  },
  
  // Calculate class average
  calculateClassAverage(grades) {
    if (!grades || grades.length === 0) return 0;
    
    const validGrades = grades.filter(grade => grade !== null && grade !== undefined && !isNaN(grade));
    if (validGrades.length === 0) return 0;
    
    const sum = validGrades.reduce((acc, grade) => acc + parseFloat(grade), 0);
    return Math.round((sum / validGrades.length) * 100) / 100;
  }
};

// UI utilities
const UIUtils = {
  // Show loading spinner
  showLoading(element, text = 'Loading...') {
    if (element) {
      element.innerHTML = `
        <div class="loading-spinner">
          <div class="spinner"></div>
          <span>${text}</span>
        </div>
      `;
    }
  },
  
  // Hide loading spinner
  hideLoading(element, content = '') {
    if (element) {
      element.innerHTML = content;
    }
  },
  
  // Show notification
  showNotification(message, type = 'info', duration = 5000) {
    const container = document.getElementById('notification-container');
    if (!container) return;
    
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-message">${message}</span>
        <button class="notification-close">&times;</button>
      </div>
    `;
    
    container.appendChild(notification);
    
    // Auto remove after duration
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, duration);
    
    // Manual close
    notification.querySelector('.notification-close').addEventListener('click', () => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    });
  },
  
  // Confirm dialog
  showConfirm(message, callback) {
    if (confirm(message)) {
      callback();
    }
  },
  
  // Format number with commas
  formatNumber(num) {
    if (num === null || num === undefined) return '0';
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
};

// Export utilities to global scope
window.DateUtils = DateUtils;
window.StringUtils = StringUtils;
window.ValidationUtils = ValidationUtils;
window.GradeUtils = GradeUtils;
window.UIUtils = UIUtils;
