// AIMS Page Router
// Handles navigation between different pages/components

const PageRouter = {
  // Router state
  state: {
    currentPage: null,
    previousPage: null,
    pageHistory: [],
    loadedComponents: new Map(),
    initialized: false,
    loading: false
  },

  // Initialize router
  init() {
    // Prevent multiple initializations
    if (this.state.initialized) {
      console.warn('PageRouter already initialized');
      return;
    }

    this.setupEventListeners();
    this.state.initialized = true;
    this.loadPage('dashboard'); // Default page
  },

  // Setup event listeners
  setupEventListeners() {
    // Handle browser back/forward
    window.addEventListener('popstate', (e) => {
      if (e.state && e.state.page) {
        this.loadPage(e.state.page, false);
      }
    });

    // Handle hash changes
    window.addEventListener('hashchange', () => {
      const hash = window.location.hash.substring(1);
      if (hash && hash !== this.state.currentPage) {
        this.loadPage(hash);
      }
    });
  },

  // Load a page with enhanced UX and academic context
  async loadPage(pageId, addToHistory = true) {
    try {
      // Prevent loading the same page multiple times or loading while already loading
      if (this.state.loading) {
        console.warn(`Page loading already in progress, ignoring request for ${pageId}`);
        return;
      }

      if (this.state.currentPage === pageId && !addToHistory) {
        return;
      }

      // Set loading state
      this.state.loading = true;

      // Update navigation state
      if (window.ModernNavigation) {
        window.ModernNavigation.setActivePage(pageId);
      }

      // Get content container
      const contentArea = document.getElementById('content-area');
      if (!contentArea) {
        console.error('Content area not found');
        return;
      }

      // Handle dashboard specially
      if (pageId === 'dashboard') {
        this.showLoadingState(contentArea);
        if (window.ModernDashboard) {
          await window.ModernDashboard.init();
        } else {
          contentArea.innerHTML = this.getDefaultDashboard();
        }
      } else {
        // Load page content directly
        this.showLoadingState(contentArea);
        const content = await this.getPageContent(pageId);

        // Update content with smooth transition
        contentArea.style.opacity = '0';
        setTimeout(() => {
          contentArea.innerHTML = content;
          contentArea.style.opacity = '1';
          contentArea.style.transition = 'opacity 0.3s ease-in-out';
        }, 150);
      }

      // Initialize page-specific functionality
      await this.initializePage(pageId);

      // Update state
      this.state.previousPage = this.state.currentPage;
      this.state.currentPage = pageId;

      // Add to history
      if (addToHistory) {
        this.state.pageHistory.push(pageId);
        window.history.pushState({ page: pageId }, '', `#${pageId}`);
      }

      // Dispatch page loaded event
      document.dispatchEvent(new CustomEvent('pageLoaded', {
        detail: { pageId, previousPage: this.state.previousPage }
      }));

    } catch (error) {
      console.error(`❌ Failed to load page ${pageId}:`, error);
      this.showErrorState(document.getElementById('content-area'), error.message);
    } finally {
      // Clear loading state
      this.state.loading = false;
    }
  },

  // Update document title (browser tab only - header title removed for cleaner UI)
  updatePageTitle(pageId) {
    this.state.currentPage = pageId;
    const title = this.getPageTitle(pageId);

    // Update document title (browser tab)
    document.title = `${title} - AIMS`;

    console.log(`📄 Document title updated: ${title}`);
  },

  // Get page title (centralized method for entire app)
  getPageTitle(pageId) {
    const titles = {
      'dashboard': 'Dashboard',
      'academic-years-management': 'Academic Years Management',
      'manage-classes-streams': 'Classes & Streams',
      'manage-streams': 'Manage Streams',
      'class-enrollments': 'Class Enrollments',
      'enroll-students': 'Enroll Students',
      'register-student': 'Register Student',
      'manage-students': 'Manage Students',
      'subject-changes': 'Subject Changes',
      'register-teacher': 'Register Teacher',
      'manage-teachers': 'Manage Teachers',
      'o-level-subjects': 'O-Level Subjects',
      'a-level-subjects': 'A-Level Subjects',
      'a-level-combinations': 'A-Level Combinations',
      'o-level-grade-boundaries': 'O-Level Grade Boundaries',
      'a-level-grade-boundaries': 'A-Level Grade Boundaries',
      'enter-ca-scores': 'Enter CA Scores',
      'enter-exam-grades': 'Enter Exam Grades',
      'generate-report-cards': 'Generate Report Cards',
      'register-admin': 'Register Admin',
      'manage-admins': 'Manage Admins',
      'school-settings': 'School Settings',
      'backup-restore': 'Backup & Restore',
      'data-import-export': 'Import/Export Data'
    };
    return titles[pageId] || 'AIMS';
  },

  // Get page content
  async getPageContent(pageId) {
    // Check if component is already loaded
    if (this.state.loadedComponents.has(pageId)) {
      const component = this.state.loadedComponents.get(pageId);
      return component.render ? component.render() : component;
    }

    // Load page content based on pageId
    switch (pageId) {
      case 'dashboard':
        // Dashboard is handled directly in loadPage method
        return this.getDefaultDashboard();

      // Academic Years Management
      case 'academic-years-management':
        return await this.loadComponent('academic-years', 'AcademicYearsManagementComponent');

      // Classes & Streams Management
      case 'manage-classes-streams':
        return await this.loadComponent('classes-streams', 'ClassesStreamsComponent');

      case 'manage-streams':
        return await this.loadComponent('classes-streams', 'ManageStreamsComponent');

      case 'enroll-students':
        return await this.loadComponent('classes-streams', 'ClassEnrollmentsComponent');

      // Subjects Management
      case 'o-level-subjects':
        return await this.loadComponent('subjects-management', 'OLevelSubjectsComponent');

      case 'a-level-subjects':
        return await this.loadComponent('subjects-management', 'ALevelSubjectsComponent');

      case 'a-level-combinations':
        return await this.loadComponent('subjects-management', 'ALevelCombinationsComponent');

      // Grade Boundaries
      case 'o-level-grade-boundaries':
        return await this.loadComponent('grade-boundaries', 'OLevelGradeBoundariesComponent');

      case 'a-level-grade-boundaries':
        return await this.loadComponent('grade-boundaries', 'ALevelGradeBoundariesComponent');

      case 'register-student':
        return await this.loadComponent('student-management', 'RegisterStudentComponent');

      case 'manage-students':
        return await this.loadComponent('student-management', 'ManageStudentsComponent');

      case 'enroll-students':
        return await this.loadComponent('student-management', 'StudentEnrollmentComponent');

      case 'subject-change-requests':
        return await this.loadComponent('student-management', 'SubjectChangeRequestsComponent');

      case 'register-teacher':
        return await this.loadComponent('teacher-management', 'RegisterTeacherComponent');

      case 'manage-teachers':
        return await this.loadComponent('teacher-management', 'ManageTeachersComponent');

      // School Settings
      case 'school-settings':
        return await this.loadComponent('school-settings', 'SchoolSettingsManagementComponent');

      // These components are not yet implemented - show placeholders
      case 'create-assessments':
      case 'term-examinations':
      case 'generate-report-cards':
      case 'register-admin':
      case 'manage-admins':
        return this.getComponentPlaceholder(this.getPageTitle(pageId));

      // Assessment Management - Additional pages
      case 'enter-ca-scores':
        return await this.loadComponent('assessment-management', 'EnterCAScoresComponent');

      case 'enter-exam-grades':
        return await this.loadComponent('assessment-management', 'EnterExamGradesComponent');

      // System Settings - Additional pages (not yet implemented)
      case 'backup-restore':
      case 'data-import-export':
        return this.getComponentPlaceholder(this.getPageTitle(pageId));

      default:
        return this.getNotFoundPage();
    }
  },

  // Load component dynamically
  async loadComponent(_module, componentName) {
    try {
      // Check if component is already loaded globally
      if (window[componentName]) {
        const component = window[componentName];

        // First render the component
        let content = '';
        if (component.render && typeof component.render === 'function') {
          content = component.render();
        } else if (typeof component === 'string') {
          content = component;
        } else {
          content = this.getComponentPlaceholder(componentName);
        }

        // Validate rendered content
        content = this.validateComponentContent(content, componentName);

        // Store the content for later initialization
        this.state.loadedComponents.set(componentName, { content, component });

        return content;
      }

      // Return placeholder if component not found
      return this.getComponentPlaceholder(componentName);

    } catch (error) {
      console.error(`Failed to load component ${componentName}:`, error);
      return this.getErrorPlaceholder(componentName, error.message);
    }
  },

  // Initialize page-specific functionality
  async initializePage(pageId) {
    // Initialize based on page type
    switch (pageId) {
      case 'dashboard':
        // Dashboard initialization is handled by ModernDashboard
        if (window.ModernDashboard && window.ModernDashboard.init) {
          await window.ModernDashboard.init();
        }
        break;

      default:
        // Initialize component if it was loaded
        await this.initializeLoadedComponent(pageId);
        // Initialize common page functionality
        this.initializeCommonFeatures();
        break;
    }
  },

  // Initialize loaded component
  async initializeLoadedComponent(pageId) {
    try {
      // Find the component that was loaded for this page
      for (const [componentName, componentData] of this.state.loadedComponents.entries()) {
        if (componentData.component && componentData.component.init && typeof componentData.component.init === 'function') {
          console.log(`🔧 Initializing component: ${componentName}`);
          await componentData.component.init();
        }
      }
    } catch (error) {
      console.error(`Failed to initialize component for page ${pageId}:`, error);
    }
  },

  // Validate component content
  validateComponentContent(content, componentName) {
    // Check if content is valid
    if (!content || typeof content !== 'string') {
      console.warn(`Component ${componentName} returned invalid content, using placeholder`);
      return this.getComponentPlaceholder(componentName);
    }

    // Check if content is empty or just whitespace
    if (content.trim().length === 0) {
      console.warn(`Component ${componentName} returned empty content, using placeholder`);
      return this.getComponentPlaceholder(componentName);
    }

    // Check if content looks like HTML
    if (!content.includes('<') || !content.includes('>')) {
      console.warn(`Component ${componentName} returned non-HTML content: ${content.substring(0, 100)}...`);
      return `
        <div class="space-y-6">
          <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">${componentName}</h2>
            <div class="p-4 bg-gray-50 rounded-lg">
              <pre class="text-sm text-gray-700">${content}</pre>
            </div>
          </div>
        </div>
      `;
    }

    return content;
  },

  // Initialize common page features
  initializeCommonFeatures() {
    // Initialize tooltips, modals, etc.
    this.initializeTooltips();
    this.initializeModals();
  },

  // Initialize tooltips
  initializeTooltips() {
    // Add tooltip functionality if needed
  },

  // Initialize modals
  initializeModals() {
    // Add modal functionality if needed
  },

  // Show loading state
  showLoadingState(container) {
    container.innerHTML = `
      <div class="flex items-center justify-center h-64">
        <div class="text-center">
          <div class="loading-spinner mx-auto mb-4"></div>
          <p class="text-gray-500">Loading...</p>
        </div>
      </div>
    `;
  },

  // Show error state
  showErrorState(container, message) {
    container.innerHTML = `
      <div class="flex items-center justify-center h-64">
        <div class="text-center">
          <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Page</h3>
          <p class="text-gray-500 mb-4">${message}</p>
          <button onclick="location.reload()" class="btn btn-primary">
            <i class="fas fa-refresh mr-2"></i>
            Reload Page
          </button>
        </div>
      </div>
    `;
  },

  // Get default dashboard
  getDefaultDashboard() {
    return `
      <div class="space-y-6">
        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to AIMS</h2>
          <p class="text-gray-600">Academic Information Management System is loading...</p>
        </div>
      </div>
    `;
  },

  // Get component placeholder
  getComponentPlaceholder(componentName) {
    return `
      <div class="space-y-6">
        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">${componentName}</h2>
          <p class="text-gray-600">This component is being developed...</p>
          <div class="mt-4 p-4 bg-blue-50 rounded-lg">
            <p class="text-blue-800 text-sm">
              <i class="fas fa-info-circle mr-2"></i>
              This feature will be available soon. The component structure is ready for implementation.
            </p>
          </div>
        </div>
      </div>
    `;
  },

  // Get error placeholder
  getErrorPlaceholder(componentName, error) {
    return `
      <div class="space-y-6">
        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">Error Loading ${componentName}</h2>
          <div class="p-4 bg-red-50 rounded-lg">
            <p class="text-red-800 text-sm">
              <i class="fas fa-exclamation-triangle mr-2"></i>
              ${error}
            </p>
          </div>
        </div>
      </div>
    `;
  },

  // Get not found page
  getNotFoundPage() {
    return `
      <div class="flex items-center justify-center h-64">
        <div class="text-center">
          <i class="fas fa-search text-gray-400 text-6xl mb-4"></i>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">Page Not Found</h3>
          <p class="text-gray-500 mb-4">The requested page could not be found.</p>
          <button onclick="PageRouter.loadPage('dashboard')" class="btn btn-primary">
            <i class="fas fa-home mr-2"></i>
            Go to Dashboard
          </button>
        </div>
      </div>
    `;
  },

  // Navigate back
  goBack() {
    if (this.state.pageHistory.length > 1) {
      this.state.pageHistory.pop(); // Remove current page
      const previousPage = this.state.pageHistory[this.state.pageHistory.length - 1];
      this.loadPage(previousPage, false);
    }
  },

  // Get current page
  getCurrentPage() {
    return this.state.currentPage;
  }
};

// Export to global scope
window.PageRouter = PageRouter;
