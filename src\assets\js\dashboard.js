// AIMS Enhanced Dashboard Component
// Clean, informative dashboard with real-time data and system admin context

const Dashboard = {
  // Dashboard state
  state: {
    stats: {},
    charts: {},
    refreshInterval: null,
    academicContext: null,
    currentAdmin: null
  },

  // Chart instances for proper cleanup
  enrollmentChart: null,
  performanceChart: null,

  // Initialize dashboard
  async init() {
    await this.loadCurrentAdmin();
    await this.loadAcademicContext();
    await this.loadDashboardData();
    this.render();
    this.initializeCharts();
  },

  // Load current admin information
  async loadCurrentAdmin() {
    try {
      const currentAdminId = this.getCurrentAdminId();
      if (currentAdminId) {
        // You could fetch admin details from API if needed
        this.state.currentAdmin = {
          id: currentAdminId,
          name: this.getAdminName()
        };
      }
    } catch (error) {
      console.error('❌ Failed to load current admin:', error);
      this.state.currentAdmin = null;
    }
  },

  // Get current admin ID from authentication
  getCurrentAdminId() {
    // Use the standardized audit fields utility
    if (window.AuditFieldsUtil) {
      return window.AuditFieldsUtil.getCurrentUserId();
    }

    // Fallback implementation
    try {
      if (window.AIMS && window.AIMS.currentUser && window.AIMS.currentUser.id) {
        return window.AIMS.currentUser.id;
      }
      return 1; // Default to system admin
    } catch (error) {
      console.error('Error getting current admin ID:', error);
      return 1;
    }
  },

  // Get admin name for display
  getAdminName() {
    try {
      const adminData = localStorage.getItem('aims_admin_data');
      if (adminData) {
        const parsed = JSON.parse(adminData);
        return `${parsed.first_name || ''} ${parsed.last_name || ''}`.trim() || 'System Admin';
      }
      return 'System Admin';
    } catch (error) {
      return 'System Admin';
    }
  },

  // Load academic context
  async loadAcademicContext() {
    try {
      // Use the correct API service
      const result = await window.AcademicAPI.getCurrentContext();

      if (result.success) {
        this.state.academicContext = result.data;
        // Also update global AIMS state
        if (window.AIMS) {
          window.AIMS.currentAcademicYear = result.data.academicYear;
          window.AIMS.currentTerm = result.data.currentTerm;
        }
      } else {
        this.state.academicContext = null;
      }

      if (window.AIMSConfig && window.AIMSConfig.get && window.AIMSConfig.get('development.debugMode')) {
        console.log('📅 Academic context loaded:', this.state.academicContext);
      }
    } catch (error) {
      console.error('❌ Failed to load academic context:', error);
      this.state.academicContext = null;
    }
  },



  // Load dashboard data
  async loadDashboardData() {
    try {
      // Load complete dashboard data from API
      const dashboardResponse = await this.fetchDashboardStats();

      if (dashboardResponse.setupRequired) {
        this.state.stats = this.getDefaultStats();
        this.state.setupRequired = true;
        this.state.academicInfo = null;
        return;
      }

      // Use real data from API and map to expected format
      const overview = dashboardResponse.overview || {};
      this.state.stats = {
        totalStudents: overview.total_students || 0,
        totalTeachers: overview.total_teachers || 0,
        totalClasses: overview.total_classes || 0,
        totalSubjects: overview.total_subjects || 0
      };
      this.state.enrollmentByLevel = dashboardResponse.class_breakdown || [];
      this.state.assessmentStats = dashboardResponse.assessments || {};
      this.state.academicContext = dashboardResponse.academic_context || null;
      this.state.academicInfo = this.extractAcademicInfo(dashboardResponse);
      this.state.setupRequired = false;

    } catch (error) {
      console.error('❌ Error loading dashboard data:', error);
      this.state.stats = this.getDefaultStats();
      this.state.setupRequired = false;
    }
  },

  // Fetch dashboard statistics
  async fetchDashboardStats() {
    try {
      // Build academic parameters including admin context
      const params = {};
      if (this.state.academicContext?.academicYear?.id) {
        params.academic_year_id = this.state.academicContext.academicYear.id;
      }
      if (this.state.academicContext?.currentTerm?.id) {
        params.term_id = this.state.academicContext.currentTerm.id;
      }
      if (this.state.currentAdmin?.id) {
        params.admin_id = this.state.currentAdmin.id;
      }

      // Use the new API service
      const result = await window.DashboardAPI.getStats(params);

      if (result.success) {
        if (window.AIMSConfig && window.AIMSConfig.get && window.AIMSConfig.get('development.debugMode')) {
          console.log('📊 Dashboard stats loaded:', result.data);
        }
        return result.data; // Return complete response, not just overview
      } else {
        return { setupRequired: true, message: 'API not available' };
      }
    } catch (error) {
      console.warn('⚠️ Failed to fetch dashboard stats:', error);
      return { setupRequired: true, message: 'Connection error' };
    }
  },


  // Get default stats for fallback
  getDefaultStats() {
    return {
      totalStudents: 0,
      totalTeachers: 0,
      totalClasses: 0,
      totalSubjects: 0
    };
  },



  // Extract academic info from dashboard response
  extractAcademicInfo(dashboardData) {
    if (dashboardData.academicContext) {
      return {
        currentYear: dashboardData.academicContext.academicYearId || 'Not Set',
        currentTerm: dashboardData.academicContext.termId || 'Not Set',
        termProgress: 65, // This could be calculated based on dates
        nextTermStart: 'N/A' // This could be fetched from terms data
      };
    }
    return null;
  },

  // Render dashboard
  render() {
    const container = document.getElementById('content-area');
    if (!container) return;

    container.innerHTML = `
      <div class="space-y-6">
        <!-- Stats Grid -->
        ${this.renderStatsGrid()}

        <!-- Charts Section -->
        ${this.renderChartsSection()}

        <!-- Enrollment by Level -->
        ${this.renderEnrollmentByLevel()}

        <!-- Assessment Statistics -->
        ${this.renderAssessmentStats()}
      </div>
    `;

    // Initialize charts after DOM is updated
    this.initializeCharts();
  },



  // Render stats grid
  renderStatsGrid() {
    const stats = this.state.stats;

    return `
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        ${this.renderStatCard('Students', stats.totalStudents || 0, 'fas fa-user-graduate', 'blue')}
        ${this.renderStatCard('Teachers', stats.totalTeachers || 0, 'fas fa-chalkboard-teacher', 'green')}
        ${this.renderStatCard('Classes', stats.totalClasses || 0, 'fas fa-door-open', 'purple')}
        ${this.renderStatCard('Subjects', stats.totalSubjects || 0, 'fas fa-book', 'orange')}
      </div>
    `;
  },

  // Render individual stat card
  renderStatCard(title, value, icon, color, subtitle) {
    const colorClasses = {
      blue: 'bg-blue-500 text-blue-600 bg-blue-50',
      green: 'bg-green-500 text-green-600 bg-green-50',
      purple: 'bg-purple-500 text-purple-600 bg-purple-50',
      orange: 'bg-orange-500 text-orange-600 bg-orange-50'
    };

    const [iconBg] = colorClasses[color].split(' ');

    return `
      <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 mb-1">${title}</p>
            <p class="text-3xl font-bold text-gray-900">${value}</p>
            ${subtitle ? `<p class="text-sm text-gray-500 mt-1">${subtitle}</p>` : ''}
          </div>
          <div class="w-12 h-12 ${iconBg} rounded-lg flex items-center justify-center">
            <i class="${icon} text-white text-xl"></i>
          </div>
        </div>
      </div>
    `;
  },



  // Render charts section
  renderChartsSection() {
    return `
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Student Enrollment Chart -->
        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Student Enrollment by Level</h3>
          <div class="h-64 relative">
            <canvas id="enrollment-chart" class="w-full h-full"></canvas>
          </div>
        </div>

        <!-- Academic Performance Chart -->
        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Assessment Performance</h3>
          <div class="h-64 relative">
            <canvas id="performance-chart" class="w-full h-full"></canvas>
          </div>
        </div>
      </div>
    `;
  },



  // Render enrollment by level
  renderEnrollmentByLevel() {
    const enrollmentData = this.state.enrollmentByLevel || [];

    return `
      <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Enrollment by Level</h3>
        ${enrollmentData.length > 0 ? `
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            ${enrollmentData.map(level => `
              <div class="text-center p-4 border border-gray-200 rounded-lg">
                <div class="text-2xl font-bold text-gray-900">${level.student_count || 0}</div>
                <div class="text-sm text-gray-600">${level.education_level_name || level.class_level_name || level.class_name || level.level}</div>
                <div class="text-xs text-gray-500 mt-1">
                  ${level.class_count || 0} ${level.class_count === 1 ? 'class' : 'classes'}
                </div>
              </div>
            `).join('')}
          </div>
        ` : `
          <div class="text-center py-8 text-gray-500">
            <i class="fas fa-users text-4xl mb-4"></i>
            <p>No enrollment data available</p>
            <p class="text-sm">Student enrollment data will appear here</p>
          </div>
        `}
      </div>
    `;
  },

  // Render assessment statistics
  renderAssessmentStats() {
    const assessmentStats = this.state.assessmentStats || {};

    return `
      <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Assessment Statistics</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">${assessmentStats.total_assessments || 0}</div>
            <div class="text-sm text-blue-900">Total Assessments</div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">${assessmentStats.total_examinations || 0}</div>
            <div class="text-sm text-green-900">Examinations</div>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">${Math.round(assessmentStats.avg_ca_score || 0)}%</div>
            <div class="text-sm text-purple-900">Avg CA Score</div>
          </div>
        </div>
      </div>
    `;
  },



  // Initialize charts with real data
  initializeCharts() {
    console.log('📊 Initializing dashboard charts...');

    // Destroy existing charts if they exist
    this.destroyExistingCharts();

    // Wait for DOM to be ready
    setTimeout(() => {
      this.initializeEnrollmentChart();
      this.initializePerformanceChart();
    }, 100);
  },

  // Destroy existing chart instances to prevent conflicts
  destroyExistingCharts() {
    // Destroy enrollment chart if it exists
    if (this.enrollmentChart) {
      this.enrollmentChart.destroy();
      this.enrollmentChart = null;
    }

    // Destroy performance chart if it exists
    if (this.performanceChart) {
      this.performanceChart.destroy();
      this.performanceChart = null;
    }
  },

  // Initialize enrollment chart
  initializeEnrollmentChart() {
    const canvas = document.getElementById('enrollment-chart');
    if (!canvas) {
      console.warn('⚠️ Enrollment chart canvas not found');
      return;
    }

    // Destroy existing chart if it exists
    if (this.enrollmentChart) {
      this.enrollmentChart.destroy();
      this.enrollmentChart = null;
    }

    const ctx = canvas.getContext('2d');
    const enrollmentData = this.state.enrollmentByLevel || [];

    if (enrollmentData.length === 0) {
      // Show no data message
      ctx.fillStyle = '#f9fafb';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = '#6b7280';
      ctx.font = '16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('No enrollment data available', canvas.width/2, canvas.height/2);
      return;
    }

    // Prepare data for chart
    const labels = enrollmentData.map(level => level.level_name || level.class_name || level.level);
    const data = enrollmentData.map(level => level.student_count || 0);
    const colors = [
      '#3B82F6', '#10B981', '#8B5CF6', '#F59E0B',
      '#EF4444', '#06B6D4', '#84CC16', '#F97316'
    ];

    // Create chart using Chart.js if available
    if (window.Chart) {
      this.enrollmentChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: labels,
          datasets: [{
            data: data,
            backgroundColor: colors.slice(0, data.length),
            borderWidth: 2,
            borderColor: '#ffffff'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 15,
                usePointStyle: true,
                font: {
                  size: 12
                }
              }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const label = context.label || '';
                  const value = context.parsed || 0;
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const percentage = ((value / total) * 100).toFixed(1);
                  return `${label}: ${value} students (${percentage}%)`;
                }
              }
            }
          }
        }
      });
    } else {
      // Fallback: Simple canvas drawing
      this.drawSimpleDonutChart(ctx, data, labels, colors, canvas.width, canvas.height);
    }
  },

  // Initialize performance chart
  initializePerformanceChart() {
    const canvas = document.getElementById('performance-chart');
    if (!canvas) {
      console.warn('⚠️ Performance chart canvas not found');
      return;
    }

    // Destroy existing chart if it exists
    if (this.performanceChart) {
      this.performanceChart.destroy();
      this.performanceChart = null;
    }

    const ctx = canvas.getContext('2d');
    const assessmentStats = this.state.assessmentStats || {};

    // Prepare data for chart
    const data = [
      assessmentStats.total_assessments || 0,
      assessmentStats.total_examinations || 0,
      Math.round(assessmentStats.avg_ca_score || 0)
    ];

    const labels = ['Assessments', 'Examinations', 'Avg CA Score (%)'];

    if (window.Chart) {
      this.performanceChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: 'Academic Performance',
            data: data,
            backgroundColor: [
              '#3B82F6',
              '#10B981',
              '#8B5CF6'
            ],
            borderColor: [
              '#2563EB',
              '#059669',
              '#7C3AED'
            ],
            borderWidth: 1,
            borderRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                color: '#f3f4f6'
              },
              ticks: {
                font: {
                  size: 11
                }
              }
            },
            x: {
              grid: {
                display: false
              },
              ticks: {
                font: {
                  size: 11
                }
              }
            }
          },
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const label = context.label || '';
                  const value = context.parsed.y || 0;
                  if (label.includes('Score')) {
                    return `${label}: ${value}%`;
                  }
                  return `${label}: ${value}`;
                }
              }
            }
          }
        }
      });
    } else {
      // Fallback: Simple canvas drawing
      this.drawSimpleBarChart(ctx, data, labels, canvas.width, canvas.height);
    }
  },

  // Simple fallback chart drawing functions
  drawSimpleDonutChart(ctx, data, labels, colors, width, height) {
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 3;
    const total = data.reduce((a, b) => a + b, 0);

    if (total === 0) {
      ctx.fillStyle = '#6b7280';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('No data available', centerX, centerY);
      return;
    }

    let currentAngle = -Math.PI / 2;

    data.forEach((value, index) => {
      const sliceAngle = (value / total) * 2 * Math.PI;

      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
      ctx.arc(centerX, centerY, radius * 0.6, currentAngle + sliceAngle, currentAngle, true);
      ctx.closePath();
      ctx.fillStyle = colors[index % colors.length];
      ctx.fill();

      currentAngle += sliceAngle;
    });

    // Draw labels
    ctx.fillStyle = '#374151';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    labels.forEach((label, index) => {
      const y = height - 30 + (index * 15);
      ctx.fillStyle = colors[index % colors.length];
      ctx.fillRect(10, y - 8, 12, 12);
      ctx.fillStyle = '#374151';
      ctx.textAlign = 'left';
      ctx.fillText(`${label}: ${data[index]}`, 25, y);
    });
  },

  drawSimpleBarChart(ctx, data, labels, width, height) {
    const maxValue = Math.max(...data, 1);
    const barWidth = (width - 60) / data.length;
    const chartHeight = height - 60;

    data.forEach((value, index) => {
      const barHeight = (value / maxValue) * chartHeight;
      const x = 30 + (index * barWidth) + (barWidth * 0.1);
      const y = height - 30 - barHeight;

      ctx.fillStyle = ['#3B82F6', '#10B981', '#8B5CF6'][index % 3];
      ctx.fillRect(x, y, barWidth * 0.8, barHeight);

      // Draw value on top of bar
      ctx.fillStyle = '#374151';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(value.toString(), x + (barWidth * 0.4), y - 5);

      // Draw label
      ctx.fillText(labels[index], x + (barWidth * 0.4), height - 10);
    });
  },

  // Manual refresh function (auto-refresh removed)
  async refresh() {
    console.log('🔄 Manually refreshing dashboard data...');
    await this.loadDashboardData();
    this.render();
  }
};

// Export to global scope (maintain backward compatibility)
window.Dashboard = Dashboard;
window.ModernDashboard = Dashboard;
