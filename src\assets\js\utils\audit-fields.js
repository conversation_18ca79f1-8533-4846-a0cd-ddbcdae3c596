/**
 * Audit Fields Utility
 * Provides consistent handling of audit fields (created_by_id, updated_by_id) across the application
 */

const AuditFieldsUtil = {
  /**
   * Get the current authenticated admin/user ID
   * @returns {number} The current user ID or 1 as fallback
   */
  getCurrentUserId() {
    try {
      // Primary method: Get from authenticated user
      if (window.AIMS && window.AIMS.currentUser && window.AIMS.currentUser.id) {
        return window.AIMS.currentUser.id;
      }

      // Fallback: try to get from AuthManager
      if (window.AuthManager && typeof window.AuthManager.getCurrentUser === 'function') {
        const user = window.AuthManager.getCurrentUser();
        if (user && user.id) {
          return user.id;
        }
      }

      // Fallback: try to decode JWT token
      const token = localStorage.getItem('aims_token');
      if (token) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          if (payload.id) {
            return payload.id;
          }
        } catch (error) {
          console.error('Error decoding JWT token:', error);
        }
      }

      // Default fallback to system admin
      console.warn('Could not determine current user ID, using default system admin (ID: 1)');
      return 1;
    } catch (error) {
      console.error('Error getting current user ID:', error);
      return 1;
    }
  },

  /**
   * Get the current authenticated user's full information
   * @returns {object|null} The current user object or null
   */
  getCurrentUser() {
    try {
      if (window.AIMS && window.AIMS.currentUser) {
        return window.AIMS.currentUser;
      }

      if (window.AuthManager && typeof window.AuthManager.getCurrentUser === 'function') {
        return window.AuthManager.getCurrentUser();
      }

      return null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  },

  /**
   * Populate hidden audit fields in a form
   * @param {string} formId - The ID of the form element
   * @param {boolean} isUpdate - Whether this is an update operation (affects updated_by_id)
   */
  populateAuditFields(formId, isUpdate = false) {
    try {
      const form = document.getElementById(formId);
      if (!form) {
        console.warn(`Form with ID '${formId}' not found`);
        return;
      }

      const currentUserId = this.getCurrentUserId();

      // Set created_by_id (for new records)
      const createdByField = form.querySelector('input[name="created_by_id"], #created_by_id');
      if (createdByField && !isUpdate) {
        createdByField.value = currentUserId;
      }

      // Set updated_by_id (for updates)
      const updatedByField = form.querySelector('input[name="updated_by_id"], #updated_by_id');
      if (updatedByField && isUpdate) {
        updatedByField.value = currentUserId;
      }
    } catch (error) {
      console.error('Error populating audit fields:', error);
    }
  },

  /**
   * Add audit fields to form data object
   * @param {object} data - The form data object
   * @param {boolean} isUpdate - Whether this is an update operation
   * @returns {object} The data object with audit fields added
   */
  addAuditFieldsToData(data, isUpdate = false) {
    try {
      const currentUserId = this.getCurrentUserId();

      if (!isUpdate) {
        // For new records, set created_by_id
        data.created_by_id = currentUserId;
      } else {
        // For updates, set updated_by_id
        data.updated_by_id = currentUserId;
      }

      return data;
    } catch (error) {
      console.error('Error adding audit fields to data:', error);
      return data;
    }
  },

  /**
   * Create hidden audit field HTML elements
   * @param {boolean} includeUpdatedBy - Whether to include updated_by_id field
   * @returns {string} HTML string for hidden audit fields
   */
  createAuditFieldsHTML(includeUpdatedBy = true) {
    const currentUserId = this.getCurrentUserId();
    
    let html = `<input type="hidden" name="created_by_id" id="created_by_id" value="${currentUserId}">`;
    
    if (includeUpdatedBy) {
      html += `<input type="hidden" name="updated_by_id" id="updated_by_id" value="">`;
    }
    
    return html;
  },

  /**
   * Format audit information for display
   * @param {object} record - The database record with audit fields
   * @returns {object} Formatted audit information
   */
  formatAuditInfo(record) {
    try {
      const auditInfo = {
        created: {
          by: record.created_by_name || `User ID: ${record.created_by_id}`,
          at: record.created_at ? new Date(record.created_at).toLocaleString() : 'Unknown'
        },
        updated: {
          by: record.updated_by_name || (record.updated_by_id ? `User ID: ${record.updated_by_id}` : 'Never updated'),
          at: record.updated_at ? new Date(record.updated_at).toLocaleString() : 'Never updated'
        }
      };

      return auditInfo;
    } catch (error) {
      console.error('Error formatting audit info:', error);
      return {
        created: { by: 'Unknown', at: 'Unknown' },
        updated: { by: 'Unknown', at: 'Unknown' }
      };
    }
  },

  /**
   * Validate that audit fields are properly set
   * @param {object} data - The form data to validate
   * @param {boolean} isUpdate - Whether this is an update operation
   * @returns {boolean} True if audit fields are valid
   */
  validateAuditFields(data, isUpdate = false) {
    try {
      if (!isUpdate && !data.created_by_id) {
        console.error('created_by_id is required for new records');
        return false;
      }

      if (isUpdate && !data.updated_by_id) {
        console.error('updated_by_id is required for updates');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error validating audit fields:', error);
      return false;
    }
  }
};

// Make it globally available
window.AuditFieldsUtil = AuditFieldsUtil;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AuditFieldsUtil;
}
