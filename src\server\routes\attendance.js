const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Mark attendance
router.post('/', async (req, res) => {
  try {
    const { class_id, attendance_date, attendance_records, teacher_id, academic_year_id, term_id } = req.body;

    // Validate required fields
    if (!class_id || !attendance_date || !attendance_records || !teacher_id || !academic_year_id || !term_id) {
      return res.status(400).json({
        success: false,
        message: 'Class ID, attendance date, attendance records, teacher ID, academic year ID, and term ID are required'
      });
    }

    // Validate attendance records format
    if (!Array.isArray(attendance_records) || attendance_records.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Attendance records must be a non-empty array'
      });
    }

    // Check if attendance already exists for this class and date
    const existingQuery = `
      SELECT id FROM attendance 
      WHERE class_id = ? AND attendance_date = ? AND academic_year_id = ? AND term_id = ?
      LIMIT 1
    `;
    
    const existingResult = await executeQuery(existingQuery, [class_id, attendance_date, academic_year_id, term_id]);
    
    if (existingResult.success && existingResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Attendance already recorded for this class and date'
      });
    }

    // Insert attendance records
    const insertPromises = attendance_records.map(record => {
      const { student_id, status, remarks } = record;
      
      if (!student_id || !status) {
        throw new Error('Each attendance record must have student_id and status');
      }

      const insertQuery = `
        INSERT INTO attendance (
          student_id, class_id, attendance_date, status, remarks,
          teacher_id, academic_year_id, term_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;

      return executeQuery(insertQuery, [
        student_id, class_id, attendance_date, status, remarks,
        teacher_id, academic_year_id, term_id
      ]);
    });

    // Execute all inserts
    const results = await Promise.all(insertPromises);
    
    // Check if all inserts were successful
    const failedInserts = results.filter(result => !result.success);
    if (failedInserts.length > 0) {
      throw new Error('Some attendance records failed to save');
    }

    res.status(201).json({
      success: true,
      message: 'Attendance recorded successfully',
      data: {
        records_saved: attendance_records.length,
        class_id,
        attendance_date
      }
    });

  } catch (error) {
    console.error('Mark attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to record attendance'
    });
  }
});

// Get attendance by class and date
router.get('/', async (req, res) => {
  try {
    const { class_id, date, academic_year_id, term_id, student_id } = req.query;
    
    let query = `
      SELECT 
        a.*,
        s.admission_number, s.first_name, s.middle_name, s.last_name,
        c.name as class_name,
        t.first_name as teacher_first_name, t.last_name as teacher_last_name
      FROM attendance a
      JOIN students s ON a.student_id = s.id
      JOIN classes c ON a.class_id = c.id
      LEFT JOIN teachers t ON a.teacher_id = t.id
      WHERE 1=1
    `;
    
    let params = [];
    
    if (class_id) {
      query += ' AND a.class_id = ?';
      params.push(class_id);
    }
    
    if (date) {
      query += ' AND a.attendance_date = ?';
      params.push(date);
    }
    
    if (academic_year_id) {
      query += ' AND a.academic_year_id = ?';
      params.push(academic_year_id);
    }
    
    if (term_id) {
      query += ' AND a.term_id = ?';
      params.push(term_id);
    }
    
    if (student_id) {
      query += ' AND a.student_id = ?';
      params.push(student_id);
    }
    
    query += ' ORDER BY a.attendance_date DESC, s.admission_number';
    
    const result = await executeQuery(query, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve attendance'
    });
  }
});

// Get student attendance summary
router.get('/student/:studentId/summary', async (req, res) => {
  try {
    const { studentId } = req.params;
    const { term_id, academic_year_id } = req.query;

    let whereClause = 'WHERE a.student_id = ?';
    let params = [studentId];
    
    if (term_id) {
      whereClause += ' AND a.term_id = ?';
      params.push(term_id);
    }
    
    if (academic_year_id) {
      whereClause += ' AND a.academic_year_id = ?';
      params.push(academic_year_id);
    }

    const summaryQuery = `
      SELECT 
        COUNT(*) as total_days,
        COUNT(CASE WHEN status = 'present' THEN 1 END) as present_days,
        COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent_days,
        COUNT(CASE WHEN status = 'late' THEN 1 END) as late_days,
        COUNT(CASE WHEN status = 'excused' THEN 1 END) as excused_days,
        ROUND((COUNT(CASE WHEN status = 'present' THEN 1 END) / COUNT(*)) * 100, 2) as attendance_percentage
      FROM attendance a
      ${whereClause}
    `;
    
    const result = await executeQuery(summaryQuery, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get student attendance summary error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve student attendance summary'
    });
  }
});

// Get class attendance summary
router.get('/class/:classId/summary', async (req, res) => {
  try {
    const { classId } = req.params;
    const { term_id, academic_year_id } = req.query;

    let whereClause = 'WHERE a.class_id = ?';
    let params = [classId];
    
    if (term_id) {
      whereClause += ' AND a.term_id = ?';
      params.push(term_id);
    }
    
    if (academic_year_id) {
      whereClause += ' AND a.academic_year_id = ?';
      params.push(academic_year_id);
    }

    const summaryQuery = `
      SELECT 
        s.id as student_id,
        s.admission_number,
        s.first_name,
        s.last_name,
        COUNT(*) as total_days,
        COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_days,
        COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_days,
        COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_days,
        COUNT(CASE WHEN a.status = 'excused' THEN 1 END) as excused_days,
        ROUND((COUNT(CASE WHEN a.status = 'present' THEN 1 END) / COUNT(*)) * 100, 2) as attendance_percentage
      FROM attendance a
      JOIN students s ON a.student_id = s.id
      ${whereClause}
      GROUP BY s.id, s.admission_number, s.first_name, s.last_name
      ORDER BY s.admission_number
    `;
    
    const result = await executeQuery(summaryQuery, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get class attendance summary error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve class attendance summary'
    });
  }
});

// Save attendance (alternative endpoint for bulk updates)
router.post('/save', async (req, res) => {
  try {
    const { attendance_data } = req.body;

    if (!Array.isArray(attendance_data) || attendance_data.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Attendance data must be a non-empty array'
      });
    }

    // Process each attendance record
    const updatePromises = attendance_data.map(async (record) => {
      const { id, status, remarks } = record;
      
      if (!id || !status) {
        throw new Error('Each record must have id and status');
      }

      const updateQuery = `
        UPDATE attendance 
        SET status = ?, remarks = ?, updated_at = NOW()
        WHERE id = ?
      `;

      return executeQuery(updateQuery, [status, remarks, id]);
    });

    // Execute all updates
    const results = await Promise.all(updatePromises);
    
    // Check if all updates were successful
    const failedUpdates = results.filter(result => !result.success);
    if (failedUpdates.length > 0) {
      throw new Error('Some attendance records failed to update');
    }

    res.json({
      success: true,
      message: 'Attendance updated successfully',
      data: {
        records_updated: attendance_data.length
      }
    });

  } catch (error) {
    console.error('Save attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save attendance'
    });
  }
});

module.exports = router;
