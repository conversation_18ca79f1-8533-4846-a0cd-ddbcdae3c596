// AIMS School Settings Management Component
// Comprehensive school settings management system

// Uses global API services: window.SchoolSettingsAPI
// Uses global config: window.AIMSConfig
// Uses environment configuration: ../config/environment.js

const SchoolSettingsComponents = {
  // Component state
  state: {
    settings: {},
    loading: false,
    currentAdmin: null
  },

  // Initialize component
  async init() {
    await this.loadCurrentAdmin();
    await this.loadInitialData();
  },

  // Load current admin information
  async loadCurrentAdmin() {
    try {
      const currentAdminId = this.getCurrentAdminId();
      if (currentAdminId) {
        this.state.currentAdmin = {
          id: currentAdminId,
          name: this.getAdminName()
        };
      }
    } catch (error) {
      console.error('Failed to load current admin:', error);
      this.state.currentAdmin = null;
    }
  },

  // Get current admin ID from authentication
  getCurrentAdminId() {
    // Use the standardized audit fields utility
    if (window.AuditFieldsUtil) {
      return window.AuditFieldsUtil.getCurrentUserId();
    }

    // Fallback implementation
    try {
      if (window.AIMS && window.AIMS.currentUser && window.AIMS.currentUser.id) {
        return window.AIMS.currentUser.id;
      }
      return 1; // Default to system admin
    } catch (error) {
      console.error('Error getting current admin ID:', error);
      return 1;
    }
  },

  // Get admin name for display
  getAdminName() {
    try {
      const adminData = localStorage.getItem('aims_admin_data');
      if (adminData) {
        const parsed = JSON.parse(adminData);
        return `${parsed.first_name || ''} ${parsed.last_name || ''}`.trim() || 'System Admin';
      }
      return 'System Admin';
    } catch (error) {
      return 'System Admin';
    }
  },

  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;

      console.log('🔄 Loading school settings data...');

      // Use the API service
      const result = await window.SchoolSettingsAPI.getAll();

      if (result.success && result.data) {
        // Convert key-value pairs to object for easier access
        this.state.settings = this.processSettingsData(result.data);
      } else {
        this.state.settings = {};
      }

      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.log('✅ School settings data loaded:', this.state.settings);
      }

    } catch (error) {
      console.error('❌ Failed to load school settings:', error);
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.error('Debug: School settings loading error details:', error);
      }
      this.state.settings = {};
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to load school settings', 'error');
      }
    } finally {
      this.state.loading = false;
    }
  },

  // Get default settings structure (based on actual schema)
  getDefaultSettings() {
    return {
      school_name: 'Nyabikoni Secondary School',
      school_motto: 'Strive for Excellence',
      school_address: 'P.O. Box 304 Kabale Uganda',
      school_email: '<EMAIL>',
      school_website: 'www.nyabikoniss.ac.ug',
      school_logo_path: '/assets/images/logo.png',
      school_contacts: '+256 772 655 176, +256 703 599 882, +256 775 475 629'
    };
  },

  // Convert settings array to object for easier access
  processSettingsData(settingsArray) {
    const settingsObj = {};
    if (Array.isArray(settingsArray)) {
      settingsArray.forEach(setting => {
        settingsObj[setting.setting_key] = {
          value: setting.setting_value,
          type: setting.setting_type,
          category: setting.category,
          description: setting.description,
          is_editable: setting.is_editable
        };
      });
    }
    return settingsObj;
  }
};

// School Settings Management Component
const SchoolSettingsManagementComponent = {
  // Render school settings interface
  render() {
    return `
      <div class="space-y-6">
        ${this.renderPageHeader()}
        
        <!-- School Basic Information -->
        ${this.renderBasicInformation()}
        
        <!-- Contact Information -->
        ${this.renderContactInformation()}
        
        <!-- Actions -->
        ${this.renderActions()}
      </div>
    `;
  },

  // Render page header
  renderPageHeader() {
    const settings = SchoolSettingsComponents.state.settings;
    const currentAdmin = SchoolSettingsComponents.state.currentAdmin;
    
    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">School Settings</h1>
            <p class="text-gray-600 mt-1">Manage your school's basic information and configuration</p>
          </div>
          <div class="text-right">
            <div class="text-sm text-gray-500">Last updated by:</div>
            <div class="font-medium text-gray-900">${currentAdmin?.name || 'System Admin'}</div>
            <div class="text-xs text-gray-500">${settings.updated_at ? new Date(settings.updated_at).toLocaleString() : 'Never'}</div>
          </div>
        </div>
      </div>
    `;
  },

  // Render basic information section
  renderBasicInformation() {
    const settings = SchoolSettingsComponents.state.settings;

    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Basic Information</h3>
        <form id="basic-info-form" class="space-y-6">
          <!-- Hidden field for system admin tracking -->
          <input type="hidden" id="updated_by_id" name="updated_by_id" value="">

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="school_name" class="block text-sm font-medium text-gray-700 mb-2">School Name *</label>
              <input type="text"
                     id="school_name"
                     name="school_name"
                     value="${settings.school_name?.value || 'Nyabikoni Secondary School'}"
                     required
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                     placeholder="Enter school name">
            </div>
            <div>
              <label for="school_email" class="block text-sm font-medium text-gray-700 mb-2">School Email</label>
              <input type="email"
                     id="school_email"
                     name="school_email"
                     value="${settings.school_email?.value || '<EMAIL>'}"
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                     placeholder="<EMAIL>">
            </div>
          </div>

          <div>
            <label for="school_address" class="block text-sm font-medium text-gray-700 mb-2">School Address</label>
            <textarea id="school_address"
                      name="school_address"
                      rows="3"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter complete school address">${settings.school_address?.value || 'P.O. Box 304 Kabale Uganda'}</textarea>
          </div>

          <div>
            <label for="school_website" class="block text-sm font-medium text-gray-700 mb-2">School Website</label>
            <input type="url"
                   id="school_website"
                   name="school_website"
                   value="${settings.school_website?.value || 'www.nyabikoniss.ac.ug'}"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                   placeholder="https://www.schoolwebsite.com">
          </div>

          <div>
            <label for="school_motto" class="block text-sm font-medium text-gray-700 mb-2">School Motto</label>
            <input type="text"
                   id="school_motto"
                   name="school_motto"
                   value="${settings.school_motto?.value || 'Strive for Excellence'}"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                   placeholder="Enter school motto">
          </div>
        </form>
      </div>
    `;
  },

  // Initialize school settings component
  async init() {
    await SchoolSettingsComponents.loadInitialData();
    this.populateSystemAdminFields();
    this.initializeEventListeners();
  },

  // Populate system admin fields
  populateSystemAdminFields() {
    const currentAdminId = SchoolSettingsComponents.getCurrentAdminId();
    
    if (currentAdminId) {
      const updatedByField = document.getElementById('updated_by_id');
      if (updatedByField) updatedByField.value = currentAdminId;
    }
  },

  // Render contact information section
  renderContactInformation() {
    const settings = SchoolSettingsComponents.state.settings;
    const contactNumbers = settings.contact_numbers || ['+256 772 655 176', '+256 703 599 882', '+256 775 475 629'];

    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Contact Information</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Contact Numbers</label>
            <div class="space-y-2">
              ${contactNumbers.map((number, index) => `
                <div class="flex items-center space-x-2">
                  <input type="tel"
                         id="contact_${index}"
                         name="contact_numbers[]"
                         value="${number}"
                         class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                         placeholder="+256 XXX XXX XXX">
                  ${index > 0 ? `
                    <button type="button" onclick="SchoolSettingsManagementComponent.removeContactNumber(${index})"
                            class="px-3 py-2 text-red-600 hover:text-red-800">
                      <i class="fas fa-trash"></i>
                    </button>
                  ` : ''}
                </div>
              `).join('')}
            </div>
            <button type="button" onclick="SchoolSettingsManagementComponent.addContactNumber()"
                    class="mt-2 text-blue-600 hover:text-blue-800 text-sm">
              <i class="fas fa-plus mr-1"></i> Add Contact Number
            </button>
          </div>
        </div>
      </div>
    `;
  },

  // Render school identity section
  renderSchoolIdentity() {
    const settings = SchoolSettingsComponents.state.settings;

    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">School Identity</h3>
        <div class="space-y-6">
          <div>
            <label for="school_motto" class="block text-sm font-medium text-gray-700 mb-2">School Motto</label>
            <input type="text"
                   id="school_motto"
                   name="school_motto"
                   value="${settings.school_motto || ''}"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                   placeholder="Enter school motto">
          </div>

          <div>
            <label for="school_vision" class="block text-sm font-medium text-gray-700 mb-2">School Vision</label>
            <textarea id="school_vision"
                      name="school_vision"
                      rows="3"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter school vision statement">${settings.school_vision || ''}</textarea>
          </div>

          <div>
            <label for="school_mission" class="block text-sm font-medium text-gray-700 mb-2">School Mission</label>
            <textarea id="school_mission"
                      name="school_mission"
                      rows="3"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter school mission statement">${settings.school_mission || ''}</textarea>
          </div>

          <div>
            <label for="school_logo" class="block text-sm font-medium text-gray-700 mb-2">School Logo</label>
            <div class="flex items-center space-x-4">
              ${settings.school_logo ? `
                <img src="${settings.school_logo}" alt="School Logo" class="w-16 h-16 object-cover rounded-lg border border-gray-300">
              ` : `
                <div class="w-16 h-16 bg-gray-100 rounded-lg border border-gray-300 flex items-center justify-center">
                  <i class="fas fa-image text-gray-400"></i>
                </div>
              `}
              <div class="flex-1">
                <input type="file"
                       id="school_logo"
                       name="school_logo"
                       accept="image/*"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <p class="text-xs text-gray-500 mt-1">Upload PNG, JPG or GIF (max 2MB)</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Render contact information section
  renderContactInformation() {
    const settings = SchoolSettingsComponents.state.settings;
    const contactNumbers = settings.school_contacts?.value || '+256 772 655 176, +256 703 599 882, +256 775 475 629';
    const contactArray = contactNumbers.split(',').map(num => num.trim());

    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Contact Information</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Contact Numbers</label>
            <div class="space-y-2" id="contact-numbers-container">
              ${contactArray.map((number, index) => `
                <div class="flex items-center space-x-2">
                  <input type="tel"
                         id="contact_${index}"
                         name="contact_numbers[]"
                         value="${number}"
                         class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                         placeholder="+256 XXX XXX XXX">
                  ${index > 0 ? `
                    <button type="button" onclick="SchoolSettingsManagementComponent.removeContactNumber(${index})"
                            class="px-3 py-2 text-red-600 hover:text-red-800">
                      <i class="fas fa-trash"></i>
                    </button>
                  ` : ''}
                </div>
              `).join('')}
            </div>
            <button type="button" onclick="SchoolSettingsManagementComponent.addContactNumber()"
                    class="mt-2 text-blue-600 hover:text-blue-800 text-sm">
              <i class="fas fa-plus mr-1"></i> Add Contact Number
            </button>
          </div>
        </div>
      </div>
    `;
  },

  // Render actions section
  renderActions() {
    return `
      <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Save Changes</h3>
            <p class="text-sm text-gray-600">Update your school settings and configuration</p>
          </div>
          <div class="flex items-center space-x-4">
            <button type="button"
                    onclick="SchoolSettingsManagementComponent.resetForm()"
                    class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
              Reset
            </button>
            <button type="button"
                    id="save-settings"
                    onclick="SchoolSettingsManagementComponent.saveSettings()"
                    class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
              <i class="fas fa-save mr-2"></i>Save Settings
            </button>
          </div>
        </div>
      </div>
    `;
  },

  // Add contact number
  addContactNumber() {
    const container = document.getElementById('contact-numbers-container');
    if (!container) return;

    const currentInputs = container.querySelectorAll('input[name="contact_numbers[]"]');
    const newIndex = currentInputs.length;

    const newContactDiv = document.createElement('div');
    newContactDiv.className = 'flex items-center space-x-2';
    newContactDiv.innerHTML = `
      <input type="tel"
             id="contact_${newIndex}"
             name="contact_numbers[]"
             value=""
             class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
             placeholder="+256 XXX XXX XXX">
      <button type="button" onclick="SchoolSettingsManagementComponent.removeContactNumber(${newIndex})"
              class="px-3 py-2 text-red-600 hover:text-red-800">
        <i class="fas fa-trash"></i>
      </button>
    `;

    container.appendChild(newContactDiv);
  },

  // Remove contact number
  removeContactNumber(index) {
    const contactInput = document.getElementById(`contact_${index}`);
    if (contactInput && contactInput.parentElement) {
      contactInput.parentElement.remove();
    }
  },

  // Reset form
  resetForm() {
    if (confirm('Are you sure you want to reset all changes? This will reload the original settings.')) {
      SchoolSettingsManagementComponent.init();
    }
  },

  // Save settings
  async saveSettings() {
    try {
      const currentAdminId = SchoolSettingsComponents.getCurrentAdminId();

      // Collect form data
      const formData = {
        school_name: document.getElementById('school_name').value,
        school_email: document.getElementById('school_email').value,
        school_address: document.getElementById('school_address').value,
        school_website: document.getElementById('school_website').value,
        school_motto: document.getElementById('school_motto').value
      };

      // Collect contact numbers
      const contactInputs = document.querySelectorAll('input[name="contact_numbers[]"]');
      const contactNumbers = Array.from(contactInputs)
        .map(input => input.value.trim())
        .filter(value => value.length > 0)
        .join(', ');

      formData.school_contacts = contactNumbers;

      // Convert to settings format
      const settingsArray = Object.entries(formData).map(([key, value]) => ({
        setting_key: key,
        setting_value: value,
        setting_type: 'string',
        category: 'school_info',
        updated_by_id: currentAdminId
      }));

      // Save via API
      const result = await window.SchoolSettingsAPI.update(settingsArray);

      if (result.success) {
        // Show success notification if available
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show('School settings updated successfully!', 'success');
        } else {
          alert('School settings updated successfully!');
        }
        await SchoolSettingsComponents.loadInitialData();
      } else {
        // Show error notification if available
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show(result.message || 'Failed to update settings', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to update settings'));
        }
      }
    } catch (error) {
      console.error('Save settings error:', error);
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.error('Debug: Save settings error details:', error);
      }
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to save settings', 'error');
      } else {
        alert('Error: Failed to save settings');
      }
    }
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Event listeners are handled via onclick attributes in the HTML
  }
};

// Export components to global scope
window.SchoolSettingsComponents = SchoolSettingsComponents;
window.SchoolSettingsManagementComponent = SchoolSettingsManagementComponent;
