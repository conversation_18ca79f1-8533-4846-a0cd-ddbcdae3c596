const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// =============================================
// SYSTEM REPORTS
// =============================================

// Get system statistics
router.get('/system/stats', async (req, res) => {
  try {
    const systemStatsQuery = `
      SELECT 
        (SELECT COUNT(*) FROM students WHERE status = 'active') as total_students,
        (SELECT COUNT(*) FROM teachers WHERE is_active = TRUE) as total_teachers,
        (SELECT COUNT(*) FROM classes WHERE is_active = TRUE) as total_classes,
        (SELECT COUNT(*) FROM subjects WHERE is_active = TRUE) as total_subjects,
        (SELECT COUNT(*) FROM continuous_assessments) as total_assessments,
        (SELECT COUNT(*) FROM student_enrollments) as total_enrollments,
        (SELECT COUNT(*) FROM system_users WHERE is_active = TRUE) as total_system_users
    `;
    
    const result = await executeQuery(systemStatsQuery);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get system stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve system statistics'
    });
  }
});

// Get user activity report
router.get('/system/user-activity', async (req, res) => {
  try {
    const activityQuery = `
      SELECT 
        su.username,
        su.first_name,
        su.last_name,
        su.email,
        su.last_login,
        su.created_at,
        CASE 
          WHEN su.last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 'Active'
          WHEN su.last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 'Recent'
          ELSE 'Inactive'
        END as activity_status
      FROM system_users su
      WHERE su.is_active = TRUE
      ORDER BY su.last_login DESC
    `;
    
    const result = await executeQuery(activityQuery);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get user activity error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve user activity report'
    });
  }
});

// =============================================
// ACADEMIC REPORTS
// =============================================

// Get class performance report
router.get('/academic/class/:classId/performance', async (req, res) => {
  try {
    const { classId } = req.params;
    const { term } = req.query;

    let whereClause = 'WHERE se.class_id = ?';
    let params = [classId];
    
    if (term) {
      whereClause += ' AND ca.term_id = ?';
      params.push(term);
    }

    const performanceQuery = `
      SELECT 
        s.admission_number,
        s.first_name,
        s.last_name,
        sub.name as subject_name,
        sub.short_name as subject_short_name,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        MAX(ca.competency_score) as highest_score,
        MIN(ca.competency_score) as lowest_score,
        COUNT(CASE WHEN ca.competency_score >= 2.5 THEN 1 END) as excellent_count,
        COUNT(CASE WHEN ca.competency_score >= 2.0 AND ca.competency_score < 2.5 THEN 1 END) as good_count,
        COUNT(CASE WHEN ca.competency_score >= 1.5 AND ca.competency_score < 2.0 THEN 1 END) as satisfactory_count,
        COUNT(CASE WHEN ca.competency_score < 1.5 THEN 1 END) as needs_improvement_count
      FROM student_enrollments se
      JOIN students s ON se.student_id = s.id
      LEFT JOIN continuous_assessments ca ON s.id = ca.student_id AND se.academic_year_id = ca.academic_year_id AND se.term_id = ca.term_id
      LEFT JOIN subjects sub ON ca.subject_id = sub.id
      ${whereClause}
      GROUP BY s.id, s.admission_number, s.first_name, s.last_name, sub.id, sub.name, sub.short_name
      ORDER BY s.admission_number, sub.name
    `;
    
    const result = await executeQuery(performanceQuery, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get class performance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve class performance report'
    });
  }
});

// Get subject performance report
router.get('/academic/subject/:subjectId/performance', async (req, res) => {
  try {
    const { subjectId } = req.params;
    const { term } = req.query;

    let whereClause = 'WHERE ca.subject_id = ?';
    let params = [subjectId];
    
    if (term) {
      whereClause += ' AND ca.term_id = ?';
      params.push(term);
    }

    const performanceQuery = `
      SELECT 
        c.name as class_name,
        l.name as level_name,
        COUNT(DISTINCT ca.student_id) as students_assessed,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        COUNT(CASE WHEN ca.competency_score >= 2.5 THEN 1 END) as excellent_count,
        COUNT(CASE WHEN ca.competency_score >= 2.0 AND ca.competency_score < 2.5 THEN 1 END) as good_count,
        COUNT(CASE WHEN ca.competency_score >= 1.5 AND ca.competency_score < 2.0 THEN 1 END) as satisfactory_count,
        COUNT(CASE WHEN ca.competency_score < 1.5 THEN 1 END) as needs_improvement_count
      FROM continuous_assessments ca
      JOIN student_enrollments se ON ca.student_id = se.student_id AND ca.academic_year_id = se.academic_year_id AND ca.term_id = se.term_id
      JOIN classes c ON se.class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      ${whereClause}
      GROUP BY c.id, c.name, cl.name, el.name
      ORDER BY cl.sort_order, c.name
    `;
    
    const result = await executeQuery(performanceQuery, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get subject performance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve subject performance report'
    });
  }
});

// =============================================
// STUDENT REPORTS
// =============================================

// Get student report card
router.get('/student/:studentId/report-card', async (req, res) => {
  try {
    const { studentId } = req.params;
    const { term } = req.query;

    if (!term) {
      return res.status(400).json({
        success: false,
        message: 'Term parameter is required'
      });
    }

    // Get student basic info
    const studentQuery = `
      SELECT 
        s.*,
        se.class_id,
        c.name as class_name,
        cl.name as class_level_name,
        el.name as education_level_name,
        st.name as stream_name,
        ay.name as academic_year_name,
        t.name as term_name
      FROM students s
      JOIN student_enrollments se ON s.id = se.student_id
      JOIN classes c ON se.class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams st ON c.stream_id = st.id
      JOIN academic_years ay ON se.academic_year_id = ay.id
      JOIN terms t ON se.term_id = t.id
      WHERE s.id = ? AND t.id = ?
    `;
    
    const studentResult = await executeQuery(studentQuery, [studentId, term]);
    
    if (!studentResult.success || studentResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student not found or not enrolled for the specified term'
      });
    }

    // Get student O-Level assessments for the term
    const oLevelAssessmentsQuery = `
      SELECT
        sub.name as subject_name,
        sub.short_name as subject_short_name,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        MAX(ca.competency_score) as highest_score,
        MIN(ca.competency_score) as lowest_score,
        'O-Level' as level_type
      FROM o_level_continuous_assessments ca
      JOIN o_level_subjects sub ON ca.subject_id = sub.id
      WHERE ca.student_id = ? AND ca.term_id = ?
      GROUP BY sub.id, sub.name, sub.short_name
      ORDER BY sub.name
    `;

    // Get student A-Level assessments for the term
    const aLevelAssessmentsQuery = `
      SELECT
        sub.name as subject_name,
        sub.short_name as subject_short_name,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        MAX(ca.competency_score) as highest_score,
        MIN(ca.competency_score) as lowest_score,
        'A-Level' as level_type
      FROM a_level_principal_continuous_assessments ca
      JOIN a_level_subjects sub ON ca.subject_id = sub.id
      WHERE ca.student_id = ? AND ca.term_id = ?
      GROUP BY sub.id, sub.name, sub.short_name
      ORDER BY sub.name
    `;
    
    // Execute both queries and combine results
    const [oLevelResult, aLevelResult] = await Promise.all([
      executeQuery(oLevelAssessmentsQuery, [studentId, term]),
      executeQuery(aLevelAssessmentsQuery, [studentId, term])
    ]);

    const allAssessments = [];
    if (oLevelResult.success) allAssessments.push(...oLevelResult.data);
    if (aLevelResult.success) allAssessments.push(...aLevelResult.data);

    const reportCard = {
      student: studentResult.data[0],
      assessments: allAssessments,
      generated_at: new Date().toISOString()
    };

    res.json({
      success: true,
      data: reportCard
    });

  } catch (error) {
    console.error('Get student report card error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve student report card'
    });
  }
});

// Get student progress report
router.get('/student/:studentId/progress', async (req, res) => {
  try {
    const { studentId } = req.params;

    // O-Level progress query
    const oLevelProgressQuery = `
      SELECT
        t.name as term_name,
        t.number as term_number,
        sub.name as subject_name,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        MAX(ca.competency_score) as highest_score,
        MIN(ca.competency_score) as lowest_score,
        'O-Level' as level_type
      FROM o_level_continuous_assessments ca
      JOIN terms t ON ca.term_id = t.id
      JOIN o_level_subjects sub ON ca.subject_id = sub.id
      WHERE ca.student_id = ?
      GROUP BY t.id, t.name, t.number, sub.id, sub.name
      ORDER BY t.number, sub.name
    `;

    // A-Level progress query
    const aLevelProgressQuery = `
      SELECT
        t.name as term_name,
        t.number as term_number,
        sub.name as subject_name,
        COUNT(ca.id) as total_assessments,
        AVG(ca.competency_score) as average_score,
        MAX(ca.competency_score) as highest_score,
        MIN(ca.competency_score) as lowest_score,
        'A-Level' as level_type
      FROM a_level_principal_continuous_assessments ca
      JOIN terms t ON ca.term_id = t.id
      JOIN a_level_subjects sub ON ca.subject_id = sub.id
      WHERE ca.student_id = ?
      GROUP BY t.id, t.name, t.number, sub.id, sub.name
      ORDER BY t.number, sub.name
    `;

    // Execute both queries and combine results
    const [oLevelResult, aLevelResult] = await Promise.all([
      executeQuery(oLevelProgressQuery, [studentId]),
      executeQuery(aLevelProgressQuery, [studentId])
    ]);

    const allProgress = [];
    if (oLevelResult.success) allProgress.push(...oLevelResult.data);
    if (aLevelResult.success) allProgress.push(...aLevelResult.data);

    res.json({
      success: true,
      data: allProgress
    });

  } catch (error) {
    console.error('Get student progress error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve student progress report'
    });
  }
});

module.exports = router;
