// Enhanced Academic Year Management Component
// Handles academic years and terms with system admin tracking

// Uses global API services: window.AcademicYearsAPI, window.TermsAPI, window.AcademicAPI
// Uses global config: window.AIMSConfig
// Uses environment configuration: ../config/environment.js

class AcademicYearManagement {
  constructor() {
    this.currentAcademicYear = null;
    this.currentTerm = null;
    this.academicYears = [];
    this.terms = [];
    this.yearOptions = [];
    this.isLoading = false;
    this.setupRequired = false;
    
    this.init();
  }

  async init() {
    try {
      await this.loadInitialData();
      this.render();
      this.attachEventListeners();
    } catch (error) {
      console.error('❌ Failed to initialize Academic Year Management:', error);
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.error('Debug: Academic year management initialization error details:', error);
      }
      this.showError('Failed to load academic year data');
    }
  }

  // Get current admin ID from authenticated user
  getCurrentAdminId() {
    // Use the standardized audit fields utility
    if (window.AuditFieldsUtil) {
      return window.AuditFieldsUtil.getCurrentUserId();
    }

    // Fallback implementation
    try {
      if (window.AIMS && window.AIMS.currentUser && window.AIMS.currentUser.id) {
        return window.AIMS.currentUser.id;
      }
      return 1; // Default to system admin
    } catch (error) {
      console.error('Error getting current admin ID:', error);
      return 1;
    }
  }

  // Populate system admin fields for academic year
  populateAcademicYearSystemAdminFields() {
    // Use the audit fields utility for consistent behavior
    if (window.AuditFieldsUtil) {
      window.AuditFieldsUtil.populateAuditFields('academicYearForm', false);
    } else {
      // Fallback
      const currentAdminId = this.getCurrentAdminId();
      if (currentAdminId) {
        const createdByField = document.getElementById('academic_year_created_by_id');
        if (createdByField) createdByField.value = currentAdminId;
      }
    }
  }

  // Populate system admin fields for term
  populateTermSystemAdminFields() {
    // Use the audit fields utility for consistent behavior
    if (window.AuditFieldsUtil) {
      window.AuditFieldsUtil.populateAuditFields('termForm', false);
    } else {
      // Fallback
      const currentAdminId = this.getCurrentAdminId();
      if (currentAdminId) {
        const createdByField = document.getElementById('term_created_by_id');
        if (createdByField) createdByField.value = currentAdminId;
      }
    }
  }

  async loadInitialData() {
    this.isLoading = true;
    
    try {
      console.log('🔄 Loading academic year management data...');

      // Load current context
      const contextResponse = await window.AcademicAPI.getCurrentContext();
      if (contextResponse.success) {
        this.currentAcademicYear = contextResponse.data.academicYear;
        this.currentTerm = contextResponse.data.currentTerm;
        this.setupRequired = contextResponse.data.setupRequired;
      }

      // Load all academic years
      const yearsResponse = await window.AcademicYearsAPI.getAll();
      if (yearsResponse.success) {
        this.academicYears = yearsResponse.data || yearsResponse;
      }

      // Load year options from ENUM
      const optionsResponse = await window.AcademicAPI.getYearOptions();
      if (optionsResponse.success) {
        this.yearOptions = optionsResponse.data || optionsResponse;
      }

      // Load terms for current academic year
      if (this.currentAcademicYear) {
        const termsResponse = await window.TermsAPI.getAll({ academic_year_id: this.currentAcademicYear.id });
        if (termsResponse.success) {
          this.terms = termsResponse.data || termsResponse;
        }
      }

      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.log('✅ Academic year management data loaded');
      }

    } finally {
      this.isLoading = false;
    }
  }

  render() {
    const container = document.getElementById('academic-year-management');
    if (!container) return;

    container.innerHTML = `
      <div class="academic-year-management">
        <!-- Header Section -->
        <div class="management-header">
          <div class="header-content">
            <div class="header-info">
              <h2 class="header-title">
                <i class="fas fa-calendar-alt"></i>
                Academic Year Management
              </h2>
              <p class="header-subtitle">Manage academic years, terms, and current academic context</p>
            </div>
            <div class="header-actions">
              <button class="btn btn-primary" id="create-academic-year-btn">
                <i class="fas fa-plus"></i>
                New Academic Year
              </button>
            </div>
          </div>
        </div>

        <!-- Current Context Card -->
        ${this.renderCurrentContext()}

        <!-- Academic Years List -->
        <div class="content-section">
          <div class="section-header">
            <h3 class="section-title">Academic Years</h3>
            <div class="section-actions">
              <button class="btn btn-outline-secondary btn-sm" id="refresh-years-btn">
                <i class="fas fa-sync-alt"></i>
                Refresh
              </button>
            </div>
          </div>
          <div class="academic-years-grid" id="academic-years-grid">
            ${this.renderAcademicYears()}
          </div>
        </div>

        <!-- Terms Section -->
        ${this.renderTermsSection()}
      </div>

      <!-- Academic Year Modal -->
      ${this.renderAcademicYearModal()}

      <!-- Term Modal -->
      ${this.renderTermModal()}
    `;

    // Show setup modal if required
    if (this.setupRequired) {
      this.showSetupModal();
    }
  }

  renderCurrentContext() {
    if (!this.currentAcademicYear && !this.currentTerm) {
      return `
        <div class="alert alert-warning">
          <div class="alert-content">
            <i class="fas fa-exclamation-triangle"></i>
            <div>
              <h4>Academic Setup Required</h4>
              <p>No active academic year or term found. Please set up the academic year and terms to continue.</p>
              <button class="btn btn-warning btn-sm mt-2" id="setup-academic-btn">
                <i class="fas fa-cog"></i>
                Setup Academic Year
              </button>
            </div>
          </div>
        </div>
      `;
    }

    return `
      <div class="current-context-card">
        <div class="context-header">
          <h3 class="context-title">Current Academic Context</h3>
          <span class="context-status active">
            <i class="fas fa-check-circle"></i>
            Active
          </span>
        </div>
        <div class="context-content">
          <div class="context-item">
            <div class="context-label">Academic Year</div>
            <div class="context-value">
              ${this.currentAcademicYear ? this.currentAcademicYear.name : 'Not Set'}
              ${this.currentAcademicYear ? `<span class="context-dates">(${this.formatDate(this.currentAcademicYear.start_date)} - ${this.formatDate(this.currentAcademicYear.end_date)})</span>` : ''}
            </div>
          </div>
          <div class="context-item">
            <div class="context-label">Current Term</div>
            <div class="context-value">
              ${this.currentTerm ? `${this.currentTerm.name} (Term ${this.currentTerm.number})` : 'Not Set'}
              ${this.currentTerm ? `<span class="context-dates">(${this.formatDate(this.currentTerm.start_date)} - ${this.formatDate(this.currentTerm.end_date)})</span>` : ''}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  renderAcademicYears() {
    if (this.isLoading) {
      return '<div class="loading-spinner">Loading academic years...</div>';
    }

    if (this.academicYears.length === 0) {
      return `
        <div class="empty-state">
          <i class="fas fa-calendar-times"></i>
          <h4>No Academic Years</h4>
          <p>Create your first academic year to get started.</p>
          <button class="btn btn-primary" id="create-first-year-btn">
            <i class="fas fa-plus"></i>
            Create Academic Year
          </button>
        </div>
      `;
    }

    return this.academicYears.map(year => `
      <div class="academic-year-card ${year.is_active ? 'active' : ''}" data-year-id="${year.id}">
        <div class="year-header">
          <div class="year-info">
            <h4 class="year-name">${year.name}</h4>
            <div class="year-dates">${this.formatDate(year.start_date)} - ${this.formatDate(year.end_date)}</div>
          </div>
          <div class="year-status">
            ${year.is_active ? '<span class="badge badge-success">Active</span>' : '<span class="badge badge-secondary">Inactive</span>'}
          </div>
        </div>
        <div class="year-meta">
          <div class="meta-item">
            <span class="meta-label">Created by:</span>
            <span class="meta-value">${year.created_by_first_name || 'System'} ${year.created_by_last_name || ''}</span>
          </div>
          <div class="meta-item">
            <span class="meta-label">Created:</span>
            <span class="meta-value">${this.formatDateTime(year.created_at)}</span>
          </div>
          ${year.updated_by_first_name ? `
            <div class="meta-item">
              <span class="meta-label">Last updated by:</span>
              <span class="meta-value">${year.updated_by_first_name} ${year.updated_by_last_name || ''}</span>
            </div>
          ` : ''}
        </div>
        <div class="year-actions">
          <button class="btn btn-sm btn-outline-primary" onclick="academicYearMgmt.editAcademicYear(${year.id})">
            <i class="fas fa-edit"></i>
            Edit
          </button>
          <button class="btn btn-sm btn-outline-info" onclick="academicYearMgmt.manageTerms(${year.id})">
            <i class="fas fa-calendar"></i>
            Terms
          </button>
          ${!year.is_active ? `
            <button class="btn btn-sm btn-outline-success" onclick="academicYearMgmt.activateYear(${year.id})">
              <i class="fas fa-play"></i>
              Activate
            </button>
          ` : ''}
        </div>
      </div>
    `).join('');
  }

  renderTermsSection() {
    if (!this.currentAcademicYear) {
      return '';
    }

    return `
      <div class="content-section">
        <div class="section-header">
          <h3 class="section-title">Terms for ${this.currentAcademicYear.name}</h3>
          <div class="section-actions">
            <button class="btn btn-outline-primary btn-sm" id="create-term-btn">
              <i class="fas fa-plus"></i>
              Add Term
            </button>
          </div>
        </div>
        <div class="terms-grid" id="terms-grid">
          ${this.renderTerms()}
        </div>
      </div>
    `;
  }

  renderTerms() {
    if (this.terms.length === 0) {
      return `
        <div class="empty-state">
          <i class="fas fa-calendar-plus"></i>
          <h4>No Terms</h4>
          <p>Add terms for the current academic year.</p>
        </div>
      `;
    }

    return this.terms.map(term => `
      <div class="term-card ${term.is_active ? 'active' : ''}" data-term-id="${term.id}">
        <div class="term-header">
          <h4 class="term-name">${term.name}</h4>
          <span class="term-number">Term ${term.number}</span>
        </div>
        <div class="term-dates">${this.formatDate(term.start_date)} - ${this.formatDate(term.end_date)}</div>
        <div class="term-status">
          ${term.is_active ? '<span class="badge badge-success">Current</span>' : '<span class="badge badge-secondary">Inactive</span>'}
        </div>
        <div class="term-actions">
          <button class="btn btn-sm btn-outline-primary" onclick="academicYearMgmt.editTerm(${term.id})">
            <i class="fas fa-edit"></i>
            Edit
          </button>
          ${!term.is_active ? `
            <button class="btn btn-sm btn-outline-success" onclick="academicYearMgmt.activateTerm(${term.id})">
              <i class="fas fa-play"></i>
              Activate
            </button>
          ` : ''}
        </div>
      </div>
    `).join('');
  }

  renderAcademicYearModal() {
    return `
      <div class="modal fade" id="academicYearModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="academicYearModalTitle">Create Academic Year</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <form id="academicYearForm">
                <!-- Hidden fields for system admin tracking -->
                <input type="hidden" id="academic_year_created_by_id" name="created_by_id" value="">
                <input type="hidden" id="academic_year_updated_by_id" name="updated_by_id" value="">

                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="yearName" class="form-label">Academic Year *</label>
                      <select class="form-select" id="yearName" required>
                        <option value="">Select Year</option>
                        ${this.yearOptions.map(year => `<option value="${year}">${year}</option>`).join('')}
                      </select>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="form-label">Status</label>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="isActive">
                        <label class="form-check-label" for="isActive">
                          Set as active academic year
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="startDate" class="form-label">Start Date *</label>
                      <input type="date" class="form-control" id="startDate" required>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="endDate" class="form-label">End Date *</label>
                      <input type="date" class="form-control" id="endDate" required>
                    </div>
                  </div>
                </div>
                
                <!-- Terms Section -->
                <div class="terms-section mt-4">
                  <h6 class="section-title">Terms Setup</h6>
                  <div id="termsContainer">
                    <!-- Terms will be added dynamically -->
                  </div>
                  <button type="button" class="btn btn-outline-secondary btn-sm" id="addTermBtn">
                    <i class="fas fa-plus"></i>
                    Add Term
                  </button>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button" class="btn btn-primary" id="saveAcademicYearBtn">
                <i class="fas fa-save"></i>
                Save Academic Year
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  renderTermModal() {
    return `
      <div class="modal fade" id="termModal" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="termModalTitle">Create Term</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <form id="termForm">
                <!-- Hidden fields for system admin tracking -->
                <input type="hidden" id="term_created_by_id" name="created_by_id" value="">
                <input type="hidden" id="term_updated_by_id" name="updated_by_id" value="">

                <div class="form-group">
                  <label for="termName" class="form-label">Term Name *</label>
                  <input type="text" class="form-control" id="termName" placeholder="e.g., Term 1" required>
                </div>
                <div class="form-group">
                  <label for="termNumber" class="form-label">Term Number *</label>
                  <select class="form-select" id="termNumber" required>
                    <option value="">Select Term Number</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                  </select>
                </div>
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="termStartDate" class="form-label">Start Date *</label>
                      <input type="date" class="form-control" id="termStartDate" required>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="termEndDate" class="form-label">End Date *</label>
                      <input type="date" class="form-control" id="termEndDate" required>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="termIsActive">
                    <label class="form-check-label" for="termIsActive">
                      Set as current term
                    </label>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button" class="btn btn-primary" id="saveTermBtn">
                <i class="fas fa-save"></i>
                Save Term
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  attachEventListeners() {
    // Academic Year buttons
    document.getElementById('create-academic-year-btn')?.addEventListener('click', () => this.showAcademicYearModal());
    document.getElementById('create-first-year-btn')?.addEventListener('click', () => this.showAcademicYearModal());
    document.getElementById('setup-academic-btn')?.addEventListener('click', () => this.showSetupModal());
    document.getElementById('refresh-years-btn')?.addEventListener('click', () => this.refreshData());

    // Term buttons
    document.getElementById('create-term-btn')?.addEventListener('click', () => this.showTermModal());

    // Modal save buttons
    document.getElementById('saveAcademicYearBtn')?.addEventListener('click', () => this.saveAcademicYear());
    document.getElementById('saveTermBtn')?.addEventListener('click', () => this.saveTerm());

    // Add term button in modal
    document.getElementById('addTermBtn')?.addEventListener('click', () => this.addTermToForm());

    // Auto-populate term name when number changes
    document.getElementById('termNumber')?.addEventListener('change', (e) => {
      const termNameInput = document.getElementById('termName');
      if (termNameInput && !termNameInput.value) {
        termNameInput.value = `Term ${e.target.value}`;
      }
    });
  }

  showAcademicYearModal(yearId = null) {
    this.currentEditingYearId = yearId;
    const modal = new bootstrap.Modal(document.getElementById('academicYearModal'));
    const title = document.getElementById('academicYearModalTitle');

    if (yearId) {
      title.textContent = 'Edit Academic Year';
      this.loadAcademicYearData(yearId);
    } else {
      title.textContent = 'Create Academic Year';
      this.resetAcademicYearForm();
      this.addDefaultTerms();
    }

    // Populate system admin fields
    this.populateAcademicYearSystemAdminFields();

    modal.show();
  }

  showTermModal(termId = null) {
    this.currentEditingTermId = termId;
    const modal = new bootstrap.Modal(document.getElementById('termModal'));
    const title = document.getElementById('termModalTitle');

    if (termId) {
      title.textContent = 'Edit Term';
      this.loadTermData(termId);
    } else {
      title.textContent = 'Create Term';
      this.resetTermForm();
    }

    // Populate system admin fields
    this.populateTermSystemAdminFields();

    modal.show();
  }

  showSetupModal() {
    this.showAcademicYearModal();
    // Auto-check the active checkbox for setup
    document.getElementById('isActive').checked = true;
  }

  addDefaultTerms() {
    const container = document.getElementById('termsContainer');
    container.innerHTML = '';

    // Add 3 default terms
    for (let i = 1; i <= 3; i++) {
      this.addTermToForm(i);
    }
  }

  addTermToForm(termNumber = null) {
    const container = document.getElementById('termsContainer');
    const termIndex = container.children.length + 1;
    const actualTermNumber = termNumber || termIndex;

    const termDiv = document.createElement('div');
    termDiv.className = 'term-form-group';
    termDiv.innerHTML = `
      <div class="card mb-3">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="mb-0">Term ${actualTermNumber}</h6>
          <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.closest('.term-form-group').remove()">
            <i class="fas fa-trash"></i>
          </button>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-label">Term Name</label>
                <input type="text" class="form-control term-name" value="Term ${actualTermNumber}" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-label">Term Number</label>
                <select class="form-select term-number" required>
                  <option value="1" ${actualTermNumber === 1 ? 'selected' : ''}>1</option>
                  <option value="2" ${actualTermNumber === 2 ? 'selected' : ''}>2</option>
                  <option value="3" ${actualTermNumber === 3 ? 'selected' : ''}>3</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-label">Start Date</label>
                <input type="date" class="form-control term-start-date" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-label">End Date</label>
                <input type="date" class="form-control term-end-date" required>
              </div>
            </div>
          </div>
          <div class="form-group">
            <div class="form-check">
              <input class="form-check-input term-is-active" type="checkbox" ${actualTermNumber === 1 ? 'checked' : ''}>
              <label class="form-check-label">Set as current term</label>
            </div>
          </div>
        </div>
      </div>
    `;

    container.appendChild(termDiv);
  }

  async saveAcademicYear() {
    try {
      const form = document.getElementById('academicYearForm');
      if (!form.checkValidity()) {
        form.reportValidity();
        return;
      }

      // Prepare form data
      let formData = {
        name: document.getElementById('yearName').value,
        start_date: document.getElementById('startDate').value,
        end_date: document.getElementById('endDate').value,
        is_active: document.getElementById('isActive').checked
      };

      // Add audit fields using the utility
      if (window.AuditFieldsUtil) {
        formData = window.AuditFieldsUtil.addAuditFieldsToData(formData, false); // false = new record
      } else {
        // Fallback
        const currentAdminId = this.getCurrentAdminId();
        if (currentAdminId) {
          formData.created_by_id = currentAdminId;
        }
      }

      // Collect terms data
      const termsContainer = document.getElementById('termsContainer');
      const termForms = termsContainer.querySelectorAll('.term-form-group');
      const terms = [];

      termForms.forEach(termForm => {
        const termData = {
          name: termForm.querySelector('.term-name').value,
          number: parseInt(termForm.querySelector('.term-number').value),
          start_date: termForm.querySelector('.term-start-date').value,
          end_date: termForm.querySelector('.term-end-date').value,
          is_active: termForm.querySelector('.term-is-active').checked
        };
        terms.push(termData);
      });

      formData.terms = terms;

      let response;
      if (this.currentEditingYearId) {
        response = await AcademicYearsAPI.update(this.currentEditingYearId, formData);
      } else {
        response = await AcademicYearsAPI.create(formData);
      }

      if (response.success) {
        this.showSuccess(this.currentEditingYearId ? 'Academic year updated successfully' : 'Academic year created successfully');
        bootstrap.Modal.getInstance(document.getElementById('academicYearModal')).hide();
        await this.refreshData();
      } else {
        throw new Error(response.message || 'Failed to save academic year');
      }

    } catch (error) {
      console.error('Save academic year error:', error);
      this.showError(error.message || 'Failed to save academic year');
    }
  }

  async saveTerm() {
    try {
      const form = document.getElementById('termForm');
      if (!form.checkValidity()) {
        form.reportValidity();
        return;
      }

      // Prepare form data
      let formData = {
        name: document.getElementById('termName').value,
        number: parseInt(document.getElementById('termNumber').value),
        academic_year_id: this.currentAcademicYear.id,
        start_date: document.getElementById('termStartDate').value,
        end_date: document.getElementById('termEndDate').value,
        is_active: document.getElementById('termIsActive').checked
      };

      // Add audit fields using the utility
      const isUpdate = !!this.currentEditingTermId;
      if (window.AuditFieldsUtil) {
        formData = window.AuditFieldsUtil.addAuditFieldsToData(formData, isUpdate);
      } else {
        // Fallback
        const currentAdminId = this.getCurrentAdminId();
        if (currentAdminId) {
          if (isUpdate) {
            formData.updated_by_id = currentAdminId;
          } else {
            formData.created_by_id = currentAdminId;
          }
        }
      }

      let response;
      if (this.currentEditingTermId) {
        response = await TermsAPI.update(this.currentEditingTermId, formData);
      } else {
        response = await TermsAPI.create(formData);
      }

      if (response.success) {
        this.showSuccess(this.currentEditingTermId ? 'Term updated successfully' : 'Term created successfully');
        bootstrap.Modal.getInstance(document.getElementById('termModal')).hide();
        await this.refreshData();
      } else {
        throw new Error(response.message || 'Failed to save term');
      }

    } catch (error) {
      console.error('Save term error:', error);
      this.showError(error.message || 'Failed to save term');
    }
  }

  async editAcademicYear(yearId) {
    this.showAcademicYearModal(yearId);
  }

  async editTerm(termId) {
    this.showTermModal(termId);
  }

  async activateYear(yearId) {
    try {
      const year = this.academicYears.find(y => y.id === yearId);
      if (!year) return;

      const confirmed = await this.showConfirmation(
        'Activate Academic Year',
        `Are you sure you want to activate ${year.name}? This will deactivate the current active year.`
      );

      if (confirmed) {
        const response = await AcademicYearsAPI.update(yearId, { ...year, is_active: true });
        if (response.success) {
          this.showSuccess('Academic year activated successfully');
          await this.refreshData();
        } else {
          throw new Error(response.message || 'Failed to activate academic year');
        }
      }
    } catch (error) {
      console.error('Activate year error:', error);
      this.showError(error.message || 'Failed to activate academic year');
    }
  }

  async activateTerm(termId) {
    try {
      const term = this.terms.find(t => t.id === termId);
      if (!term) return;

      const confirmed = await this.showConfirmation(
        'Activate Term',
        `Are you sure you want to activate ${term.name}? This will deactivate the current active term.`
      );

      if (confirmed) {
        const response = await TermsAPI.update(termId, { ...term, is_active: true });
        if (response.success) {
          this.showSuccess('Term activated successfully');
          await this.refreshData();
        } else {
          throw new Error(response.message || 'Failed to activate term');
        }
      }
    } catch (error) {
      console.error('Activate term error:', error);
      this.showError(error.message || 'Failed to activate term');
    }
  }

  async manageTerms(yearId) {
    const year = this.academicYears.find(y => y.id === yearId);
    if (!year) return;

    // Load terms for this year
    try {
      const response = await TermsAPI.getAll({ academic_year_id: yearId });
      if (response.success) {
        this.terms = response.data;
        this.currentAcademicYear = year;
        this.render(); // Re-render to show terms section
      }
    } catch (error) {
      console.error('Load terms error:', error);
      this.showError('Failed to load terms');
    }
  }

  async refreshData() {
    await this.loadInitialData();
    this.render();
  }

  resetAcademicYearForm() {
    document.getElementById('academicYearForm').reset();
    document.getElementById('termsContainer').innerHTML = '';
  }

  resetTermForm() {
    document.getElementById('termForm').reset();
  }

  loadAcademicYearData(yearId) {
    const year = this.academicYears.find(y => y.id === yearId);
    if (!year) return;

    document.getElementById('yearName').value = year.name;
    document.getElementById('startDate').value = year.start_date;
    document.getElementById('endDate').value = year.end_date;
    document.getElementById('isActive').checked = year.is_active;
  }

  loadTermData(termId) {
    const term = this.terms.find(t => t.id === termId);
    if (!term) return;

    document.getElementById('termName').value = term.name;
    document.getElementById('termNumber').value = term.number;
    document.getElementById('termStartDate').value = term.start_date;
    document.getElementById('termEndDate').value = term.end_date;
    document.getElementById('termIsActive').checked = term.is_active;
  }

  formatDate(dateString) {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  formatDateTime(dateString) {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  showSuccess(message) {
    // Use the AIMSDesignSystem notification system
    if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
      window.AIMSDesignSystem.notifications.show(message, 'success');
    } else {
      alert(message);
    }
  }

  showError(message) {
    // Use the AIMSDesignSystem notification system
    if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
      window.AIMSDesignSystem.notifications.show(message, 'error');
    } else {
      alert('Error: ' + message);
    }
  }

  async showConfirmation(title, message) {
    // Use the global confirmation system if available
    if (window.showConfirmation) {
      return await window.showConfirmation(title, message);
    } else {
      return confirm(message);
    }
  }
}

// Initialize the Academic Year Management component
let academicYearMgmt;

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  if (document.getElementById('academic-year-management')) {
    academicYearMgmt = new AcademicYearManagement();
  }
});

// Make it globally available
window.AcademicYearManagement = AcademicYearManagement;
