#!/usr/bin/env node

// AIMS System Setup Script
// Initializes the database and sets up the system

const fs = require('fs');
const path = require('path');
const { initializeDatabase, isDatabaseInitialized } = require('./src/database/init');
const { testConnection } = require('./src/database/connection');

console.log('🚀 AIMS System Setup');
console.log('====================');

async function setup() {
  try {
    // Check if .env file exists
    const envPath = path.join(__dirname, '.env');
    if (!fs.existsSync(envPath)) {
      console.log('⚠️  .env file not found. Creating from .env.example...');
      
      const envExamplePath = path.join(__dirname, '.env.example');
      if (fs.existsSync(envExamplePath)) {
        fs.copyFileSync(envExamplePath, envPath);
        console.log('✅ .env file created. Please update the database credentials.');
        console.log('📝 Edit .env file with your MariaDB/MySQL credentials and run setup again.');
        return;
      } else {
        console.log('❌ .env.example file not found. Please create .env file manually.');
        return;
      }
    }

    // Load environment variables
    require('dotenv').config();

    console.log('🔍 Checking database connection...');
    
    // Test database connection
    const isConnected = await testConnection();
    if (!isConnected) {
      console.log('❌ Database connection failed. Please check your .env configuration.');
      console.log('📝 Make sure MariaDB/MySQL is running and credentials are correct.');
      return;
    }

    console.log('✅ Database connection successful!');

    // Check if database is already initialized
    const isInitialized = await isDatabaseInitialized();
    if (isInitialized) {
      console.log('ℹ️  Database is already initialized.');
      console.log('🎉 AIMS system is ready to use!');
      console.log('');
      console.log('📋 Next steps:');
      console.log('   1. Run: npm run server:dev (to start the backend server)');
      console.log('   2. Run: npm start (to start the Electron app)');
      console.log('   3. Login with: username=admin, password=admin123');
      console.log('   4. Change the default password after first login');
      return;
    }

    console.log('🗄️  Initializing database...');
    
    // Initialize database
    const initResult = await initializeDatabase();
    
    if (initResult.success) {
      console.log('✅ Database initialized successfully!');
      console.log('');
      console.log('🎉 AIMS System Setup Complete!');
      console.log('');
      console.log('📋 Default Login Credentials:');
      console.log('   Username: admin');
      console.log('   Password: admin123');
      console.log('');
      console.log('⚠️  IMPORTANT: Change the default password after first login!');
      console.log('');
      console.log('🚀 To start the system:');
      console.log('   1. Backend Server: npm run server:dev');
      console.log('   2. Electron App: npm start');
      console.log('');
      console.log('📖 For more information, see the documentation.');
    } else {
      console.log('❌ Database initialization failed:', initResult.error);
      console.log('');
      console.log('🔧 Troubleshooting:');
      console.log('   1. Check database credentials in .env file');
      console.log('   2. Ensure MariaDB/MySQL is running');
      console.log('   3. Verify database user has CREATE privileges');
      console.log('   4. Check the error message above for specific issues');
    }

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('   1. Check that all dependencies are installed: npm install');
    console.log('   2. Verify MariaDB/MySQL is running');
    console.log('   3. Check .env file configuration');
    console.log('   4. Ensure database user has proper privileges');
  }
}

// ASCII Art Banner
console.log(`
   ___    ____  __  ___   _____
  / _ |  /  _/ /  |/  /  / ___/
 / __ | _/ /  / /|_/ /  (__  ) 
/_/ |_|/___/ /_/  /_/  /____/  
                               
Academic Information Management System
`);

// Run setup
setup().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Setup error:', error);
  process.exit(1);
});
