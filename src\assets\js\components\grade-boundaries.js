// AIMS Grade Boundaries Management Components
// Comprehensive grade boundary management for O-Level and A-Level

// Uses global API services: window.GradeBoundariesAPI
// Uses global config: window.AIMSConfig
// Uses environment configuration: ../config/environment.js

const GradeBoundariesComponents = {
  // Component state
  state: {
    gradeBoundaries: [],
    loading: false
  },

  // Initialize component
  async init() {
    await this.loadInitialData();
  },

  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;

      // Use the API services for O-Level and A-Level
      const [oLevelBoundaries, aLevelPrincipalBoundaries, aLevelSubsidiaryBoundaries] = await Promise.all([
        window.GradeBoundariesAPI.oLevel.getAll(),
        window.GradeBoundariesAPI.aLevelPrincipal.getAll(),
        window.GradeBoundariesAPI.aLevelSubsidiary.getAll()
      ]);

      // Combine all boundaries with level indicators
      const allBoundaries = [
        ...(oLevelBoundaries.data || []).map(b => ({ ...b, level: 'o_level' })),
        ...(aLevelPrincipalBoundaries.data || []).map(b => ({ ...b, level: 'a_level_principal' })),
        ...(aLevelSubsidiaryBoundaries.data || []).map(b => ({ ...b, level: 'a_level_subsidiary' }))
      ];

      this.state.gradeBoundaries = { success: true, data: allBoundaries };

      if (window.Config && window.Config.get && window.Config.get('development.debugMode')) {
        console.log('Grade boundaries data loaded:', {
          oLevel: oLevelBoundaries,
          aLevelPrincipal: aLevelPrincipalBoundaries,
          aLevelSubsidiary: aLevelSubsidiaryBoundaries
        });
      }

    } catch (error) {
      console.error('Failed to load initial data:', error);
      if (window.showNotification) {
        window.showNotification('Failed to load grade boundaries data', 'error');
      } else {
        alert('Error: Failed to load grade boundaries data');
      }
    } finally {
      this.state.loading = false;
    }
  }
};

// O-Level Grade Boundaries Component
const OLevelGradeBoundariesComponent = {
  // Render O-Level grade boundaries interface
  render() {
    return `
      <div class="space-y-6">
        ${AIMSDesignSystem.layouts.pageHeader(
          'O-Level Grade Boundaries',
          'Manage grade boundaries for O-Level (UCE) assessment system',
          [
            { icon: 'fas fa-chart-bar', value: this.getGradeBoundariesCount(), label: 'Grade Levels', color: 'blue' }
          ]
        )}

        <!-- Grade Boundaries Information -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-info-circle text-blue-600 text-xl"></i>
              </div>
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">O-Level Assessment System</h3>
              <div class="text-sm text-gray-600 space-y-2">
                <p><strong>Final Grade Calculation:</strong> CA (20%) + Term Exam (80%)</p>
                <p><strong>CA Component:</strong> Average of all continuous assessments (Topic assessments, Activities of Integration, Projects, Assignments, Group work, Practical exercises)</p>
                <p><strong>Grade Boundaries:</strong> Used to convert final percentage scores to letter grades (A, B, C, D, E)</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Grade Boundaries Management -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Grade Boundaries Configuration</h3>
            ${AIMSDesignSystem.forms.button('edit-boundaries', 'Edit Boundaries', 'primary', {
              icon: 'fas fa-edit',
              onclick: 'OLevelGradeBoundariesComponent.enableEditing()'
            })}
          </div>

          <!-- Grade Boundaries Table -->
          <div class="overflow-hidden border border-gray-200 rounded-lg">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Min %</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Max %</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descriptor</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody id="grade-boundaries-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Grade boundaries will be populated here -->
              </tbody>
            </table>
          </div>

          <!-- Edit Actions -->
          <div id="edit-actions" class="hidden mt-6 flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            ${AIMSDesignSystem.forms.button('cancel-edit', 'Cancel', 'secondary', {
              onclick: 'OLevelGradeBoundariesComponent.cancelEditing()'
            })}
            ${AIMSDesignSystem.forms.button('save-boundaries', 'Save Changes', 'primary', {
              onclick: 'OLevelGradeBoundariesComponent.saveBoundaries()'
            })}
          </div>
        </div>

        <!-- Grade Distribution Preview -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">Grade Distribution Preview</h3>
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            ${this.renderGradeDistributionCards()}
          </div>
        </div>

        <!-- Grade Boundaries History -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">Recent Changes</h3>
          <div id="boundaries-history" class="space-y-3">
            <!-- History will be populated here -->
          </div>
        </div>
      </div>
    `;
  },

  // Initialize O-Level grade boundaries component
  async init() {
    await GradeBoundariesComponents.loadInitialData();
    this.populateGradeBoundariesTable();
    this.loadBoundariesHistory();
  },

  // Get current admin ID from authentication
  getCurrentAdminId() {
    // Use the standardized audit fields utility
    if (window.AuditFieldsUtil) {
      return window.AuditFieldsUtil.getCurrentUserId();
    }

    // Fallback implementation
    try {
      if (window.AIMS && window.AIMS.currentUser && window.AIMS.currentUser.id) {
        return window.AIMS.currentUser.id;
      }
      return 1; // Default to system admin
    } catch (error) {
      console.error('Error getting current admin ID:', error);
      return 1;
    }
  },

  // Get grade boundaries count
  getGradeBoundariesCount() {
    return GradeBoundariesComponents.state.gradeBoundaries.data?.length || 5;
  },

  // Populate grade boundaries table
  populateGradeBoundariesTable() {
    const tbody = document.getElementById('grade-boundaries-table-body');
    if (!tbody) return;

    const boundaries = GradeBoundariesComponents.state.gradeBoundaries.data || this.getDefaultBoundaries();
    
    tbody.innerHTML = boundaries.map(boundary => `
      <tr>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <span class="inline-flex items-center justify-center w-8 h-8 rounded-full ${this.getGradeBadgeClass(boundary.grade_letter)} font-bold text-sm">
              ${boundary.grade_letter}
            </span>
            <span class="ml-3 text-sm font-medium text-gray-900">${boundary.grade_letter}</span>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <input type="number" 
                 id="min_${boundary.grade_letter}"
                 value="${boundary.min_percentage}"
                 min="0" 
                 max="100" 
                 step="0.01"
                 disabled
                 class="w-20 px-3 py-2 border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500">
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <input type="number" 
                 id="max_${boundary.grade_letter}"
                 value="${boundary.max_percentage}"
                 min="0" 
                 max="100" 
                 step="0.01"
                 disabled
                 class="w-20 px-3 py-2 border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500">
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <input type="text" 
                 id="descriptor_${boundary.grade_letter}"
                 value="${boundary.grade_descriptor}"
                 disabled
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500">
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <input type="text" 
                 id="description_${boundary.grade_letter}"
                 value="${boundary.detailed_description}"
                 disabled
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500">
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            boundary.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }">
            ${boundary.is_active ? 'Active' : 'Inactive'}
          </span>
        </td>
      </tr>
    `).join('');
  },

  // Get default boundaries if none exist
  getDefaultBoundaries() {
    return [
      { grade_letter: 'A', min_percentage: 80, max_percentage: 100, grade_descriptor: 'Excellent', detailed_description: 'Outstanding performance demonstrating comprehensive understanding', is_active: true },
      { grade_letter: 'B', min_percentage: 70, max_percentage: 79.99, grade_descriptor: 'Very Good', detailed_description: 'Very good performance demonstrating good understanding', is_active: true },
      { grade_letter: 'C', min_percentage: 60, max_percentage: 69.99, grade_descriptor: 'Good', detailed_description: 'Good performance demonstrating satisfactory understanding', is_active: true },
      { grade_letter: 'D', min_percentage: 50, max_percentage: 59.99, grade_descriptor: 'Satisfactory', detailed_description: 'Satisfactory performance demonstrating basic understanding', is_active: true },
      { grade_letter: 'E', min_percentage: 0, max_percentage: 49.99, grade_descriptor: 'Unsatisfactory', detailed_description: 'Unsatisfactory performance requiring improvement', is_active: true }
    ];
  },

  // Get grade badge CSS class
  getGradeBadgeClass(grade) {
    const classes = {
      'A': 'bg-green-500 text-white',
      'B': 'bg-blue-500 text-white',
      'C': 'bg-yellow-500 text-white',
      'D': 'bg-orange-500 text-white',
      'E': 'bg-red-500 text-white'
    };
    return classes[grade] || 'bg-gray-500 text-white';
  },

  // Render grade distribution cards
  renderGradeDistributionCards() {
    const boundaries = GradeBoundariesComponents.state.gradeBoundaries.data || this.getDefaultBoundaries();
    
    return boundaries.map(boundary => `
      <div class="text-center p-4 border border-gray-200 rounded-lg">
        <div class="inline-flex items-center justify-center w-12 h-12 rounded-full ${this.getGradeBadgeClass(boundary.grade_letter)} font-bold text-lg mb-2">
          ${boundary.grade_letter}
        </div>
        <div class="text-sm font-medium text-gray-900">${boundary.grade_descriptor}</div>
        <div class="text-xs text-gray-500">${boundary.min_percentage}% - ${boundary.max_percentage}%</div>
      </div>
    `).join('');
  },

  // Enable editing mode
  enableEditing() {
    // Enable all input fields
    const inputs = document.querySelectorAll('#grade-boundaries-table-body input');
    inputs.forEach(input => {
      input.disabled = false;
      input.classList.remove('disabled:bg-gray-50', 'disabled:text-gray-500');
    });

    // Show edit actions
    document.getElementById('edit-actions').classList.remove('hidden');
    
    // Hide edit button
    document.getElementById('edit-boundaries').style.display = 'none';
  },

  // Cancel editing mode
  cancelEditing() {
    // Reload the table to reset values
    this.populateGradeBoundariesTable();
    
    // Hide edit actions
    document.getElementById('edit-actions').classList.add('hidden');
    
    // Show edit button
    document.getElementById('edit-boundaries').style.display = 'inline-flex';
  },

  // Save boundaries
  async saveBoundaries() {
    const boundaries = [];
    const grades = ['A', 'B', 'C', 'D', 'E'];

    // Get current admin ID for tracking
    const currentAdminId = this.getCurrentAdminId();

    // Collect data from form
    grades.forEach(grade => {
      boundaries.push({
        grade_letter: grade,
        min_percentage: parseFloat(document.getElementById(`min_${grade}`).value),
        max_percentage: parseFloat(document.getElementById(`max_${grade}`).value),
        grade_descriptor: document.getElementById(`descriptor_${grade}`).value,
        detailed_description: document.getElementById(`description_${grade}`).value,
        updated_by_id: currentAdminId // Include system admin tracking
      });
    });

    // Validate boundaries
    if (!this.validateBoundaries(boundaries)) {
      return;
    }

    try {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-boundaries', true);
      }

      // Use the O-Level API service
      const result = await window.GradeBoundariesAPI.oLevel.update(boundaries);

      if (result.success) {
        // Show success notification if available
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show('O-Level grade boundaries updated successfully!', 'success');
        } else {
          alert('O-Level grade boundaries updated successfully!');
        }
        await GradeBoundariesComponents.loadInitialData();
        this.cancelEditing();
        this.loadBoundariesHistory();
      } else {
        if (window.showNotification) {
          window.showNotification(result.message || 'Failed to update boundaries', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to update boundaries'));
        }
      }
    } catch (error) {
      console.error('Save boundaries error:', error);
      if (window.showNotification) {
        window.showNotification('Failed to update boundaries', 'error');
      } else {
        alert('Error: Failed to update boundaries');
      }
    } finally {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-boundaries', false);
      }
    }
  },

  // Validate boundaries
  validateBoundaries(boundaries) {
    // Check for overlaps and gaps
    for (let i = 0; i < boundaries.length - 1; i++) {
      const current = boundaries[i];
      const next = boundaries[i + 1];
      
      if (current.min_percentage >= current.max_percentage) {
        AIMSDesignSystem.notifications.show(`Invalid range for grade ${current.grade_letter}: Min must be less than Max`, 'error');
        return false;
      }
      
      if (current.min_percentage !== next.max_percentage + 0.01 && i < boundaries.length - 2) {
        AIMSDesignSystem.notifications.show(`Gap or overlap detected between grades ${current.grade_letter} and ${next.grade_letter}`, 'error');
        return false;
      }
    }
    
    return true;
  },

  // Load boundaries history
  async loadBoundariesHistory() {
    try {
      // Use the new O-Level API service
      const result = await GradeBoundariesAPI.oLevel.getHistory();

      if (result.success) {
        this.renderBoundariesHistory(result.data);
      }
    } catch (error) {
      console.error('Load history error:', error);
    }
  },

  // Render boundaries history
  renderBoundariesHistory(history) {
    const container = document.getElementById('boundaries-history');
    if (!container) return;

    if (!history || history.length === 0) {
      container.innerHTML = '<p class="text-gray-500 text-center py-4">No recent changes</p>';
      return;
    }

    container.innerHTML = history.slice(0, 5).map(change => `
      <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
        <div>
          <div class="text-sm font-medium text-gray-900">${change.description}</div>
          <div class="text-xs text-gray-500">by ${change.changed_by} • ${new Date(change.changed_at).toLocaleString()}</div>
        </div>
        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
          ${change.change_type}
        </span>
      </div>
    `).join('');
  }
};

// A-Level Grade Boundaries Component
const ALevelGradeBoundariesComponent = {
  // Render A-Level grade boundaries interface
  render() {
    return `
      <div class="space-y-6">
        ${AIMSDesignSystem.layouts.pageHeader(
          'A-Level Grade Boundaries',
          'Manage grade boundaries for A-Level (UACE) assessment system'
        )}

        <!-- A-Level Assessment Information -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-graduation-cap text-purple-600 text-xl"></i>
              </div>
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">A-Level Assessment System (UACE)</h3>
              <div class="text-sm text-gray-600 space-y-2">
                <p><strong>Paper-based Grading:</strong> Each subject has 1-4 papers depending on the subject</p>
                <p><strong>Principal Subject Grades:</strong> A(6pts), B(5pts), C(4pts), D(3pts), E(2pts), O(1pt), F(0pts)</p>
                <p><strong>Subsidiary Subject Grades:</strong> D1-C6(1pt), P7-F9(0pts)</p>
                <p><strong>Total UACE Points:</strong> Best 3 Principal subjects + Subsidiary subjects</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Principal Subjects Grade Boundaries -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Principal Subject Grade Boundaries</h3>
            ${AIMSDesignSystem.forms.button('edit-principal-boundaries', 'Edit Boundaries', 'primary', {
              icon: 'fas fa-edit',
              onclick: 'ALevelGradeBoundariesComponent.enablePrincipalEditing()'
            })}
          </div>

          <div class="overflow-hidden border border-gray-200 rounded-lg">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Points</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Min %</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Max %</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                </tr>
              </thead>
              <tbody id="principal-boundaries-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Principal grade boundaries will be populated here -->
              </tbody>
            </table>
          </div>

          <div id="principal-edit-actions" class="hidden mt-6 flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            ${AIMSDesignSystem.forms.button('cancel-principal-edit', 'Cancel', 'secondary', {
              onclick: 'ALevelGradeBoundariesComponent.cancelPrincipalEditing()'
            })}
            ${AIMSDesignSystem.forms.button('save-principal-boundaries', 'Save Changes', 'primary', {
              onclick: 'ALevelGradeBoundariesComponent.savePrincipalBoundaries()'
            })}
          </div>
        </div>

        <!-- Subsidiary Subjects Grade Boundaries -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Subsidiary Subject Grade Boundaries</h3>
            ${AIMSDesignSystem.forms.button('edit-subsidiary-boundaries', 'Edit Boundaries', 'primary', {
              icon: 'fas fa-edit',
              onclick: 'ALevelGradeBoundariesComponent.enableSubsidiaryEditing()'
            })}
          </div>

          <div class="overflow-hidden border border-gray-200 rounded-lg">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Points</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Min %</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Max %</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                </tr>
              </thead>
              <tbody id="subsidiary-boundaries-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Subsidiary grade boundaries will be populated here -->
              </tbody>
            </table>
          </div>

          <div id="subsidiary-edit-actions" class="hidden mt-6 flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            ${AIMSDesignSystem.forms.button('cancel-subsidiary-edit', 'Cancel', 'secondary', {
              onclick: 'ALevelGradeBoundariesComponent.cancelSubsidiaryEditing()'
            })}
            ${AIMSDesignSystem.forms.button('save-subsidiary-boundaries', 'Save Changes', 'primary', {
              onclick: 'ALevelGradeBoundariesComponent.saveSubsidiaryBoundaries()'
            })}
          </div>
        </div>

        <!-- Points Calculator -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">UACE Points Calculator</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Principal Subjects -->
            <div>
              <h4 class="font-medium text-gray-900 mb-4">Principal Subjects (Best 3)</h4>
              <div class="space-y-3">
                ${[1, 2, 3].map(i => `
                  <div class="flex items-center space-x-3">
                    <label class="text-sm text-gray-600 w-20">Subject ${i}:</label>
                    ${AIMSDesignSystem.forms.select(`principal_${i}`, '', [
                      { value: '', label: 'Select Grade' },
                      { value: '6', label: 'A (6 points)' },
                      { value: '5', label: 'B (5 points)' },
                      { value: '4', label: 'C (4 points)' },
                      { value: '3', label: 'D (3 points)' },
                      { value: '2', label: 'E (2 points)' },
                      { value: '1', label: 'O (1 point)' },
                      { value: '0', label: 'F (0 points)' }
                    ], '')}
                  </div>
                `).join('')}
              </div>
            </div>

            <!-- Subsidiary Subject -->
            <div>
              <h4 class="font-medium text-gray-900 mb-4">Subsidiary Subject</h4>
              <div class="space-y-3">
                <div class="flex items-center space-x-3">
                  <label class="text-sm text-gray-600 w-20">Subject:</label>
                  ${AIMSDesignSystem.forms.select('subsidiary_1', '', [
                    { value: '', label: 'Select Grade' },
                    { value: '1', label: 'D1-C6 (1 point)' },
                    { value: '0', label: 'P7-F9 (0 points)' }
                  ], '')}
                </div>
              </div>
            </div>
          </div>

          <div class="mt-6 pt-6 border-t border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                ${AIMSDesignSystem.forms.button('calculate-points', 'Calculate Total Points', 'primary', {
                  onclick: 'ALevelGradeBoundariesComponent.calculatePoints()'
                })}
              </div>
              <div class="text-right">
                <div class="text-sm text-gray-600">Total UACE Points</div>
                <div id="total-points" class="text-2xl font-bold text-blue-600">0</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize A-Level grade boundaries component
  async init() {
    await GradeBoundariesComponents.loadInitialData();
    this.populatePrincipalBoundariesTable();
    this.populateSubsidiaryBoundariesTable();
    this.initializeEventListeners();
  },

  // Get current admin ID from authentication (same as O-Level component)
  getCurrentAdminId() {
    // Use the standardized audit fields utility
    if (window.AuditFieldsUtil) {
      return window.AuditFieldsUtil.getCurrentUserId();
    }

    // Fallback implementation
    try {
      if (window.AIMS && window.AIMS.currentUser && window.AIMS.currentUser.id) {
        return window.AIMS.currentUser.id;
      }
      return 1; // Default to system admin
    } catch (error) {
      console.error('Error getting current admin ID:', error);
      return 1;
    }
  },

  // Populate principal boundaries table
  populatePrincipalBoundariesTable() {
    const tbody = document.getElementById('principal-boundaries-table-body');
    if (!tbody) return;

    const principalBoundaries = this.getPrincipalBoundaries();

    tbody.innerHTML = principalBoundaries.map(boundary => `
      <tr>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <span class="inline-flex items-center justify-center w-8 h-8 rounded-full ${this.getALevelGradeBadgeClass(boundary.grade)} font-bold text-sm">
              ${boundary.grade}
            </span>
            <span class="ml-3 text-sm font-medium text-gray-900">${boundary.grade}</span>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <span class="inline-flex px-2 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-800">
            ${boundary.points}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <input type="number"
                 id="principal_min_${boundary.grade}"
                 value="${boundary.min_percentage}"
                 min="0"
                 max="100"
                 step="0.01"
                 disabled
                 class="w-20 px-3 py-2 border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500">
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <input type="number"
                 id="principal_max_${boundary.grade}"
                 value="${boundary.max_percentage}"
                 min="0"
                 max="100"
                 step="0.01"
                 disabled
                 class="w-20 px-3 py-2 border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500">
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <input type="text"
                 id="principal_desc_${boundary.grade}"
                 value="${boundary.description}"
                 disabled
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500">
        </td>
      </tr>
    `).join('');
  },

  // Populate subsidiary boundaries table
  populateSubsidiaryBoundariesTable() {
    const tbody = document.getElementById('subsidiary-boundaries-table-body');
    if (!tbody) return;

    const subsidiaryBoundaries = this.getSubsidiaryBoundaries();

    tbody.innerHTML = subsidiaryBoundaries.map(boundary => `
      <tr>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <span class="inline-flex items-center justify-center w-8 h-8 rounded-full ${this.getSubsidiaryGradeBadgeClass(boundary.grade)} font-bold text-xs">
              ${boundary.grade}
            </span>
            <span class="ml-3 text-sm font-medium text-gray-900">${boundary.grade}</span>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <span class="inline-flex px-2 py-1 text-sm font-semibold rounded-full bg-purple-100 text-purple-800">
            ${boundary.points}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <input type="number"
                 id="subsidiary_min_${boundary.grade.replace('-', '_')}"
                 value="${boundary.min_percentage}"
                 min="0"
                 max="100"
                 step="0.01"
                 disabled
                 class="w-20 px-3 py-2 border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500">
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <input type="number"
                 id="subsidiary_max_${boundary.grade.replace('-', '_')}"
                 value="${boundary.max_percentage}"
                 min="0"
                 max="100"
                 step="0.01"
                 disabled
                 class="w-20 px-3 py-2 border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500">
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <input type="text"
                 id="subsidiary_desc_${boundary.grade.replace('-', '_')}"
                 value="${boundary.description}"
                 disabled
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500">
        </td>
      </tr>
    `).join('');
  },

  // Get principal boundaries
  getPrincipalBoundaries() {
    return [
      { grade: 'A', points: 6, min_percentage: 80, max_percentage: 100, description: 'Excellent performance' },
      { grade: 'B', points: 5, min_percentage: 70, max_percentage: 79.99, description: 'Very good performance' },
      { grade: 'C', points: 4, min_percentage: 60, max_percentage: 69.99, description: 'Good performance' },
      { grade: 'D', points: 3, min_percentage: 50, max_percentage: 59.99, description: 'Satisfactory performance' },
      { grade: 'E', points: 2, min_percentage: 40, max_percentage: 49.99, description: 'Weak performance' },
      { grade: 'O', points: 1, min_percentage: 30, max_percentage: 39.99, description: 'Very weak performance' },
      { grade: 'F', points: 0, min_percentage: 0, max_percentage: 29.99, description: 'Failure' }
    ];
  },

  // Get subsidiary boundaries
  getSubsidiaryBoundaries() {
    return [
      { grade: 'D1-C6', points: 1, min_percentage: 50, max_percentage: 100, description: 'Pass' },
      { grade: 'P7-F9', points: 0, min_percentage: 0, max_percentage: 49.99, description: 'Fail' }
    ];
  },

  // Get A-Level grade badge CSS class
  getALevelGradeBadgeClass(grade) {
    const classes = {
      'A': 'bg-green-500 text-white',
      'B': 'bg-blue-500 text-white',
      'C': 'bg-yellow-500 text-white',
      'D': 'bg-orange-500 text-white',
      'E': 'bg-red-500 text-white',
      'O': 'bg-gray-500 text-white',
      'F': 'bg-black text-white'
    };
    return classes[grade] || 'bg-gray-500 text-white';
  },

  // Get subsidiary grade badge CSS class
  getSubsidiaryGradeBadgeClass(grade) {
    const classes = {
      'D1-C6': 'bg-green-500 text-white',
      'P7-F9': 'bg-red-500 text-white'
    };
    return classes[grade] || 'bg-gray-500 text-white';
  },

  // Enable principal editing
  enablePrincipalEditing() {
    const inputs = document.querySelectorAll('#principal-boundaries-table-body input');
    inputs.forEach(input => {
      input.disabled = false;
      input.classList.remove('disabled:bg-gray-50', 'disabled:text-gray-500');
    });
    document.getElementById('principal-edit-actions').classList.remove('hidden');
    document.getElementById('edit-principal-boundaries').style.display = 'none';
  },

  // Cancel principal editing
  cancelPrincipalEditing() {
    this.populatePrincipalBoundariesTable();
    document.getElementById('principal-edit-actions').classList.add('hidden');
    document.getElementById('edit-principal-boundaries').style.display = 'inline-flex';
  },

  // Save principal boundaries
  async savePrincipalBoundaries() {
    const boundaries = [];
    const grades = ['A', 'B', 'C', 'D', 'E', 'O', 'F'];

    // Get current admin ID for tracking
    const currentAdminId = this.getCurrentAdminId();

    // Collect data from form
    grades.forEach(grade => {
      boundaries.push({
        grade_letter: grade,
        min_percentage: parseFloat(document.getElementById(`principal_min_${grade}`).value),
        max_percentage: parseFloat(document.getElementById(`principal_max_${grade}`).value),
        description: document.getElementById(`principal_desc_${grade}`).value,
        updated_by_id: currentAdminId // Include system admin tracking
      });
    });

    try {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-principal-boundaries', true);
      }

      // Use the new A-Level Principal API service
      const result = await GradeBoundariesAPI.aLevelPrincipal.update(boundaries);

      if (result.success) {
        if (window.showNotification) {
          window.showNotification('A-Level Principal boundaries updated successfully!', 'success');
        } else {
          alert('A-Level Principal boundaries updated successfully!');
        }
        await GradeBoundariesComponents.loadInitialData();
        this.cancelPrincipalEditing();
      } else {
        if (window.showNotification) {
          window.showNotification(result.message || 'Failed to update principal boundaries', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to update principal boundaries'));
        }
      }
    } catch (error) {
      console.error('Save principal boundaries error:', error);
      if (window.showNotification) {
        window.showNotification('Failed to update principal boundaries', 'error');
      } else {
        alert('Error: Failed to update principal boundaries');
      }
    } finally {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-principal-boundaries', false);
      }
    }
  },

  // Enable subsidiary editing
  enableSubsidiaryEditing() {
    const inputs = document.querySelectorAll('#subsidiary-boundaries-table-body input');
    inputs.forEach(input => {
      input.disabled = false;
      input.classList.remove('disabled:bg-gray-50', 'disabled:text-gray-500');
    });
    document.getElementById('subsidiary-edit-actions').classList.remove('hidden');
    document.getElementById('edit-subsidiary-boundaries').style.display = 'none';
  },

  // Cancel subsidiary editing
  cancelSubsidiaryEditing() {
    this.populateSubsidiaryBoundariesTable();
    document.getElementById('subsidiary-edit-actions').classList.add('hidden');
    document.getElementById('edit-subsidiary-boundaries').style.display = 'inline-flex';
  },

  // Save subsidiary boundaries
  async saveSubsidiaryBoundaries() {
    const boundaries = [];
    const grades = ['D1-C6', 'P7-F9'];

    // Get current admin ID for tracking
    const currentAdminId = this.getCurrentAdminId();

    // Collect data from form
    grades.forEach(grade => {
      const gradeId = grade.replace('-', '_');
      boundaries.push({
        grade_letter: grade,
        min_percentage: parseFloat(document.getElementById(`subsidiary_min_${gradeId}`).value),
        max_percentage: parseFloat(document.getElementById(`subsidiary_max_${gradeId}`).value),
        description: document.getElementById(`subsidiary_desc_${gradeId}`).value,
        updated_by_id: currentAdminId // Include system admin tracking
      });
    });

    try {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-subsidiary-boundaries', true);
      }

      // Note: A-Level Subsidiary boundaries are typically fixed, but we'll allow updates
      if (window.showNotification) {
        window.showNotification('A-Level Subsidiary boundaries are fixed by UNEB standards', 'info');
      } else {
        alert('Info: A-Level Subsidiary boundaries are fixed by UNEB standards');
      }

      this.cancelSubsidiaryEditing();
    } catch (error) {
      console.error('Save subsidiary boundaries error:', error);
      if (window.showNotification) {
        window.showNotification('Failed to update subsidiary boundaries', 'error');
      } else {
        alert('Error: Failed to update subsidiary boundaries');
      }
    } finally {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-subsidiary-boundaries', false);
      }
    }
  },

  // Calculate UACE points
  calculatePoints() {
    let totalPoints = 0;

    // Add principal subjects points
    for (let i = 1; i <= 3; i++) {
      const points = parseInt(document.getElementById(`principal_${i}`).value) || 0;
      totalPoints += points;
    }

    // Add subsidiary subject points
    const subsidiaryPoints = parseInt(document.getElementById('subsidiary_1').value) || 0;
    totalPoints += subsidiaryPoints;

    // Update display
    document.getElementById('total-points').textContent = totalPoints;
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Add event listeners for points calculator
    ['principal_1', 'principal_2', 'principal_3', 'subsidiary_1'].forEach(selectId => {
      const select = document.getElementById(selectId);
      if (select) {
        select.addEventListener('change', () => {
          this.calculatePoints();
        });
      }
    });
  }
};

// Export components to global scope
window.OLevelGradeBoundariesComponent = OLevelGradeBoundariesComponent;
window.ALevelGradeBoundariesComponent = ALevelGradeBoundariesComponent;
window.GradeBoundariesComponents = GradeBoundariesComponents;
