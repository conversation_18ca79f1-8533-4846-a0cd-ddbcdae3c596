{"name": "aims", "version": "1.0.0", "main": "src/main.js", "scripts": {"setup": "node setup.js", "start": "electron .", "dev": "nodemon --exec electron .", "server": "node src/server/server.js", "server:dev": "nodemon src/server/server.js", "build": "electron-builder", "test": "echo \"Error: no test specified\" && exit 1", "init-db": "node -e \"require('dotenv').config(); require('./src/database/init').initializeDatabase().then(result => { console.log(result.success ? '✅ Success: ' + result.message : '❌ Error: ' + result.error); process.exit(result.success ? 0 : 1); }).catch(err => { console.error('❌ Fatal error:', err.message); process.exit(1); })\"", "reset-db": "node -e \"const {dropDatabase, initializeDatabase} = require('./src/database/init'); dropDatabase().then(() => initializeDatabase()).then(() => process.exit(0))\""}, "keywords": ["education", "school-management", "uganda", "o-level", "a-level", "electron", "mysql"], "author": "AIMS Development Team", "license": "MIT", "description": "Academic Information Management System for Uganda Secondary Schools", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "csv-parser": "^3.2.0", "csv-writer": "^1.6.0", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "pdfkit": "^0.17.1"}, "devDependencies": {"electron": "^36.4.0", "nodemon": "^3.1.10"}}