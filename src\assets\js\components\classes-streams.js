// AIMS Classes & Streams Management Components
// Comprehensive class and stream management system

// Uses global API services: window.ClassesAPI, window.StreamsAPI, window.LevelsAPI, window.AcademicYearsAPI
// Uses global config: window.AIMSConfig
// Uses environment configuration: ../config/environment.js

const ClassesStreamsComponents = {
  // Component state
  state: {
    classes: [],
    streams: [],
    levels: [],
    academicYears: [],
    loading: false
  },

  // Initialize component
  async init() {
    await this.loadInitialData();
  },

  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;

      // Use the API service with configuration
      console.log('🔄 Loading classes and streams data...');

      const [classes, streams, levels, academicYears] = await Promise.all([
        window.ClassesAPI.getAll(),
        window.StreamsAPI.getAll(),
        window.LevelsAPI.getAll(),
        window.AcademicYearsAPI.getAll()
      ]);

      this.state.classes = classes;
      this.state.streams = streams;
      this.state.levels = levels;
      this.state.academicYears = academicYears;

      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.log('✅ Classes & Streams data loaded:', { classes, streams, levels, academicYears });
      }

    } catch (error) {
      console.error('❌ Failed to load initial data:', error);
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.error('Debug: Classes and streams loading error details:', error);
      }
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to load data', 'error');
      }
    } finally {
      this.state.loading = false;
    }
  }
};

// Manage Streams Component
const ManageStreamsComponent = {
  // Render manage streams interface
  render() {
    return `
      <div class="space-y-6">
        ${AIMSDesignSystem.layouts.pageHeader(
          'Manage Streams',
          'Create and manage class streams for organizing students into different academic tracks',
          [
            { icon: 'fas fa-door-open', value: ClassesStreamsComponents.state.classes.data?.length || 0, label: 'Total Classes', color: 'blue' },
            { icon: 'fas fa-users', value: ClassesStreamsComponents.state.streams.data?.length || 0, label: 'Total Streams', color: 'green' }
          ]
        )}

        <!-- Stream Management Actions -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Stream Management</h3>
            ${AIMSDesignSystem.forms.button('add-stream', 'Add New Stream', 'primary', {
              icon: 'fas fa-plus',
              onclick: 'ManageStreamsComponent.showAddStreamModal()'
            })}
          </div>

          <!-- Academic Year Selection -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            ${AIMSDesignSystem.forms.select('academic_year_filter', 'Academic Year', [], '')}
            ${AIMSDesignSystem.forms.select('level_filter', 'Level Filter', [], '')}
            ${AIMSDesignSystem.forms.input('search_streams', 'Search Streams', '', {
              placeholder: 'Search by stream name...',
              icon: 'fas fa-search'
            })}
          </div>
        </div>

        <!-- Classes and Streams Overview -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- O-Level Classes -->
          <div class="bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-blue-50">
              <h3 class="text-lg font-semibold text-blue-900">O-Level Classes (S.1 - S.4)</h3>
              <p class="text-sm text-blue-700">Optional streams for class organization</p>
            </div>
            <div id="o-level-classes-container" class="p-6">
              <!-- O-Level classes will be populated here -->
            </div>
          </div>

          <!-- A-Level Classes -->
          <div class="bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-green-50">
              <h3 class="text-lg font-semibold text-green-900">A-Level Classes (S.5 - S.6)</h3>
              <p class="text-sm text-green-700">Required streams: Arts and Sciences</p>
            </div>
            <div id="a-level-classes-container" class="p-6">
              <!-- A-Level classes will be populated here -->
            </div>
          </div>
        </div>

        <!-- All Streams Table -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">All Streams</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stream Name</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Classes</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody id="streams-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Streams will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Add Stream Modal -->
      <div id="add-stream-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-xl bg-white">
          <div class="mt-3">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
              <h3 class="text-lg font-semibold text-gray-900">Add New Stream</h3>
              <button onclick="ManageStreamsComponent.closeAddStreamModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
              </button>
            </div>
            <form id="add-stream-form" class="mt-6 space-y-6">
              <!-- Hidden fields for system admin tracking -->
              <input type="hidden" id="stream_created_by_id" name="created_by_id" value="">

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                ${AIMSDesignSystem.forms.input('stream_name', 'Stream Name', '', {
                  required: true,
                  placeholder: 'e.g., Arts, Sciences, A, B, C'
                })}
                ${AIMSDesignSystem.forms.input('stream_code', 'Stream Code', '', {
                  required: true,
                  placeholder: 'e.g., ART, SCI, A, B'
                })}
                ${AIMSDesignSystem.forms.select('stream_type', 'Stream Type', [
                  { value: '', label: 'Select Stream Type' },
                  { value: 'Arts', label: 'Arts (A-Level)' },
                  { value: 'Sciences', label: 'Sciences (A-Level)' },
                  { value: 'General', label: 'General (O-Level)' }
                ], '', { required: true })}
                ${AIMSDesignSystem.forms.select('applicable_levels', 'Applicable Levels', [], '', { 
                  required: true,
                  multiple: true 
                })}
              </div>
              <div>
                ${AIMSDesignSystem.forms.textarea('description', 'Description', '', {
                  placeholder: 'Optional description of the stream...',
                  rows: 3
                })}
              </div>
              <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                ${AIMSDesignSystem.forms.button('cancel-stream', 'Cancel', 'secondary', {
                  type: 'button',
                  onclick: 'ManageStreamsComponent.closeAddStreamModal()'
                })}
                ${AIMSDesignSystem.forms.button('save-stream', 'Create Stream', 'primary', {
                  type: 'submit'
                })}
              </div>
            </form>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize manage streams component
  async init() {
    await ClassesStreamsComponents.loadInitialData();
    this.populateDropdowns();
    this.populateSystemAdminFields();
    this.populateClassesOverview();
    this.populateStreamsTable();
    this.initializeEventListeners();
  },

  // Populate system admin fields
  populateSystemAdminFields() {
    const currentAdminId = this.getCurrentAdminId();

    if (currentAdminId) {
      const createdByField = document.getElementById('stream_created_by_id');
      if (createdByField) createdByField.value = currentAdminId;
    }
  },

  // Get current admin ID from authentication
  getCurrentAdminId() {
    // Use the standardized audit fields utility
    if (window.AuditFieldsUtil) {
      return window.AuditFieldsUtil.getCurrentUserId();
    }

    // Fallback implementation
    try {
      if (window.AIMS && window.AIMS.currentUser && window.AIMS.currentUser.id) {
        return window.AIMS.currentUser.id;
      }
      return 1; // Default to system admin
    } catch (error) {
      console.error('Error getting current admin ID:', error);
      return 1;
    }
  },

  // Populate dropdown fields
  populateDropdowns() {
    // Populate academic years
    const academicYearSelect = document.getElementById('academic_year_filter');
    if (academicYearSelect) {
      academicYearSelect.innerHTML = '<option value="">All Academic Years</option>';
      ClassesStreamsComponents.state.academicYears.data?.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.name;
        if (year.is_active) option.selected = true;
        academicYearSelect.appendChild(option);
      });
    }

    // Populate education levels
    const levelSelect = document.getElementById('level_filter');
    if (levelSelect) {
      levelSelect.innerHTML = '<option value="">All Education Levels</option>';
      ClassesStreamsComponents.state.levels.data?.forEach(level => {
        const option = document.createElement('option');
        option.value = level.code; // Use code instead of id
        option.textContent = level.display_name;
        levelSelect.appendChild(option);
      });
    }

    // Populate applicable levels in modal
    const applicableLevelsSelect = document.getElementById('applicable_levels');
    if (applicableLevelsSelect) {
      applicableLevelsSelect.innerHTML = '';
      ClassesStreamsComponents.state.levels.data?.forEach(level => {
        const option = document.createElement('option');
        option.value = level.id;
        option.textContent = level.display_name;
        applicableLevelsSelect.appendChild(option);
      });
    }
  },

  // Populate classes overview
  populateClassesOverview() {
    this.populateOLevelClasses();
    this.populateALevelClasses();
  },

  // Populate O-Level classes
  populateOLevelClasses() {
    const container = document.getElementById('o-level-classes-container');
    if (!container) return;

    const oLevelClasses = ClassesStreamsComponents.state.classes.data?.filter(cls =>
      cls.education_level_code === 'o_level'
    ) || [];

    if (oLevelClasses.length === 0) {
      container.innerHTML = '<p class="text-gray-500 text-center py-4">No O-Level classes found</p>';
      return;
    }

    container.innerHTML = `
      <div class="space-y-4">
        ${oLevelClasses.map(cls => `
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">${cls.name}</h4>
                <p class="text-sm text-gray-500">
                  ${cls.stream_name || 'No stream assigned'} • 
                  ${cls.current_enrollment || 0} students
                </p>
              </div>
              <div class="flex items-center space-x-2">
                <button onclick="ManageStreamsComponent.assignStream(${cls.id})" 
                        class="text-blue-600 hover:text-blue-900 text-sm">
                  ${cls.stream_name ? 'Change Stream' : 'Assign Stream'}
                </button>
              </div>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  },

  // Populate A-Level classes
  populateALevelClasses() {
    const container = document.getElementById('a-level-classes-container');
    if (!container) return;

    const aLevelClasses = ClassesStreamsComponents.state.classes.data?.filter(cls =>
      cls.education_level_code === 'a_level'
    ) || [];

    if (aLevelClasses.length === 0) {
      container.innerHTML = '<p class="text-gray-500 text-center py-4">No A-Level classes found</p>';
      return;
    }

    container.innerHTML = `
      <div class="space-y-4">
        ${aLevelClasses.map(cls => `
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-gray-900">${cls.name}</h4>
                <p class="text-sm text-gray-500">
                  ${cls.stream_name || 'No stream assigned'} • 
                  ${cls.current_enrollment || 0} students
                </p>
              </div>
              <div class="flex items-center space-x-2">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  cls.stream_name ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }">
                  ${cls.stream_name ? 'Stream Assigned' : 'No Stream'}
                </span>
                <button onclick="ManageStreamsComponent.assignStream(${cls.id})" 
                        class="text-blue-600 hover:text-blue-900 text-sm">
                  ${cls.stream_name ? 'Change Stream' : 'Assign Stream'}
                </button>
              </div>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  },

  // Populate streams table
  populateStreamsTable() {
    const tbody = document.getElementById('streams-table-body');
    if (!tbody) return;

    const streams = ClassesStreamsComponents.state.streams.data || [];
    
    if (streams.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="7" class="px-6 py-12 text-center text-gray-500">
            <i class="fas fa-users text-4xl mb-4 text-gray-300"></i>
            <p class="text-lg font-medium">No streams found</p>
            <p class="text-sm">Create new streams to organize your classes.</p>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = streams.map(stream => `
      <tr class="hover:bg-gray-50">
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm font-medium text-gray-900">${stream.name}</div>
          <div class="text-sm text-gray-500">${stream.description || 'No description'}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getStreamTypeBadgeClass(stream.stream_type)}">
            ${stream.stream_type}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${stream.classes_count || 0}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${stream.students_count || 0}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          <div class="font-medium">${stream.created_by_first_name || 'System'} ${stream.created_by_last_name || ''}</div>
          <div class="text-gray-500">${stream.created_at ? new Date(stream.created_at).toLocaleDateString() : 'N/A'}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            stream.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }">
            ${stream.is_active ? 'Active' : 'Inactive'}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div class="flex items-center justify-end space-x-2">
            <button onclick="ManageStreamsComponent.editStream(${stream.id})" 
                    class="text-indigo-600 hover:text-indigo-900" title="Edit">
              <i class="fas fa-edit"></i>
            </button>
            <button onclick="ManageStreamsComponent.toggleStreamStatus(${stream.id})" 
                    class="text-yellow-600 hover:text-yellow-900" title="Toggle Status">
              <i class="fas fa-toggle-${stream.is_active ? 'on' : 'off'}"></i>
            </button>
            <button onclick="ManageStreamsComponent.deleteStream(${stream.id})" 
                    class="text-red-600 hover:text-red-900" title="Delete">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </td>
      </tr>
    `).join('');
  },

  // Get stream type badge CSS class
  getStreamTypeBadgeClass(streamType) {
    const classes = {
      'Arts': 'bg-purple-100 text-purple-800',
      'Sciences': 'bg-blue-100 text-blue-800',
      'General': 'bg-gray-100 text-gray-800'
    };
    return classes[streamType] || 'bg-gray-100 text-gray-800';
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Add stream form submission
    const addStreamForm = document.getElementById('add-stream-form');
    if (addStreamForm) {
      addStreamForm.addEventListener('submit', this.handleAddStream.bind(this));
    }

    // Search and filter inputs
    const searchInput = document.getElementById('search_streams');
    if (searchInput) {
      searchInput.addEventListener('input', () => {
        this.populateStreamsTable();
      });
    }

    ['academic_year_filter', 'level_filter'].forEach(filterId => {
      const filterElement = document.getElementById(filterId);
      if (filterElement) {
        filterElement.addEventListener('change', () => {
          this.populateClassesOverview();
          this.populateStreamsTable();
        });
      }
    });
  },

  // Show add stream modal
  showAddStreamModal() {
    document.getElementById('add-stream-modal').classList.remove('hidden');
  },

  // Close add stream modal
  closeAddStreamModal() {
    document.getElementById('add-stream-modal').classList.add('hidden');
    document.getElementById('add-stream-form').reset();
  },

  // Handle add stream form submission
  async handleAddStream(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    let data = Object.fromEntries(formData.entries());

    // Add audit fields using the utility
    if (window.AuditFieldsUtil) {
      data = window.AuditFieldsUtil.addAuditFieldsToData(data, false); // false = new record
    } else {
      // Fallback
      const currentAdminId = this.getCurrentAdminId();
      if (currentAdminId) {
        data.created_by_id = currentAdminId;
      }
    }

    // Get selected applicable levels
    const applicableLevels = Array.from(document.querySelectorAll('#applicable_levels option:checked'))
      .map(option => option.value);

    data.applicable_levels = applicableLevels;

    try {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-stream', true);
      }

      // Use the API service
      const result = await StreamsAPI.create(data);

      if (result.success) {
        if (window.showNotification) {
          window.showNotification('Stream created successfully!', 'success');
        } else {
          alert('Stream created successfully!');
        }
        this.closeAddStreamModal();
        this.populateSystemAdminFields(); // Re-populate hidden fields
        await ClassesStreamsComponents.loadInitialData();
        this.populateClassesOverview();
        this.populateStreamsTable();
      } else {
        if (window.showNotification) {
          window.showNotification(result.message || 'Failed to create stream', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to create stream'));
        }
      }
    } catch (error) {
      console.error('Create stream error:', error);
      if (window.showNotification) {
        window.showNotification('Failed to create stream', 'error');
      } else {
        alert('Error: Failed to create stream');
      }
    } finally {
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.forms) {
        window.AIMSDesignSystem.forms.setButtonLoading('save-stream', false);
      }
    }
  },

  // Assign stream to class
  assignStream(classId) {
    console.log('Assign stream to class:', classId);
  },

  // Edit stream
  editStream(streamId) {
    console.log('Edit stream:', streamId);
  },

  // Toggle stream status
  async toggleStreamStatus(streamId) {
    try {
      // Use the API service
      const result = await StreamsAPI.toggleStatus(streamId);

      if (result.success) {
        if (window.showNotification) {
          window.showNotification('Stream status updated successfully', 'success');
        } else {
          alert('Stream status updated successfully');
        }
        await ClassesStreamsComponents.loadInitialData();
        this.populateStreamsTable();
      } else {
        if (window.showNotification) {
          window.showNotification(result.message || 'Failed to update stream status', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to update stream status'));
        }
      }
    } catch (error) {
      console.error('Toggle status error:', error);
      if (window.showNotification) {
        window.showNotification('Failed to update stream status', 'error');
      } else {
        alert('Error: Failed to update stream status');
      }
    }
  },

  // Delete stream
  async deleteStream(streamId) {
    if (!confirm('Are you sure you want to delete this stream? This action cannot be undone.')) {
      return;
    }

    try {
      // Use the API service
      const result = await StreamsAPI.delete(streamId);

      if (result.success) {
        if (window.showNotification) {
          window.showNotification('Stream deleted successfully', 'success');
        } else {
          alert('Stream deleted successfully');
        }
        await ClassesStreamsComponents.loadInitialData();
        this.populateClassesOverview();
        this.populateStreamsTable();
      } else {
        if (window.showNotification) {
          window.showNotification(result.message || 'Failed to delete stream', 'error');
        } else {
          alert('Error: ' + (result.message || 'Failed to delete stream'));
        }
      }
    } catch (error) {
      console.error('Delete error:', error);
      if (window.showNotification) {
        window.showNotification('Failed to delete stream', 'error');
      } else {
        alert('Error: Failed to delete stream');
      }
    }
  }
};

// Export components to global scope
window.ManageStreamsComponent = ManageStreamsComponent;
window.ClassesStreamsComponents = ClassesStreamsComponents;
