const express = require('express');
const bcrypt = require('bcryptjs');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get all system users
router.get('/', async (req, res) => {
  try {
    const query = `
      SELECT 
        id, username, email, first_name, last_name, middle_name,
        role, phone_number, national_id, date_of_birth, gender,
        address, profile_picture, is_active, last_login,
        created_at, updated_at
      FROM system_users
      ORDER BY created_at DESC
    `;
    
    const result = await executeQuery(query);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get system users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve system users'
    });
  }
});

// Get system user by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT 
        id, username, email, first_name, last_name, middle_name,
        role, phone_number, national_id, date_of_birth, gender,
        address, profile_picture, is_active, last_login,
        created_at, updated_at
      FROM system_users
      WHERE id = ?
    `;
    
    const result = await executeQuery(query, [id]);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'System user not found'
      });
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get system user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve system user'
    });
  }
});

// Create new system user
router.post('/', async (req, res) => {
  try {
    const {
      username, email, password, first_name, last_name, middle_name,
      phone_number, national_id, date_of_birth, gender, address
    } = req.body;

    // Validate required fields
    if (!username || !email || !password || !first_name || !last_name) {
      return res.status(400).json({
        success: false,
        message: 'Username, email, password, first name, and last name are required'
      });
    }

    // Check if username or email already exists
    const checkQuery = `
      SELECT id FROM system_users 
      WHERE username = ? OR email = ?
    `;
    
    const checkResult = await executeQuery(checkQuery, [username, email]);
    
    if (!checkResult.success) {
      throw new Error(checkResult.error);
    }

    if (checkResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Username or email already exists'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Insert new user
    const insertQuery = `
      INSERT INTO system_users (
        username, email, password, first_name, last_name, middle_name,
        role, phone_number, national_id, date_of_birth, gender, address,
        is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, TRUE, NOW(), NOW())
    `;

    const insertResult = await executeQuery(insertQuery, [
      username, email, hashedPassword, first_name, last_name, middle_name,
      'system_admin', phone_number, national_id, date_of_birth, gender, address
    ]);

    if (!insertResult.success) {
      throw new Error(insertResult.error);
    }

    // Get the created user (without password)
    const newUserQuery = `
      SELECT 
        id, username, email, first_name, last_name, middle_name,
        role, phone_number, national_id, date_of_birth, gender,
        address, is_active, created_at
      FROM system_users
      WHERE id = ?
    `;

    const newUserResult = await executeQuery(newUserQuery, [insertResult.data.insertId]);

    res.status(201).json({
      success: true,
      message: 'System user created successfully',
      data: newUserResult.data[0]
    });

  } catch (error) {
    console.error('Create system user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create system user'
    });
  }
});

// Update system user
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      username, email, first_name, last_name, middle_name,
      phone_number, national_id, date_of_birth, gender, address, is_active
    } = req.body;

    // Check if user exists
    const checkQuery = 'SELECT id FROM system_users WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);
    
    if (!checkResult.success) {
      throw new Error(checkResult.error);
    }

    if (checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'System user not found'
      });
    }

    // Check if username or email already exists for other users
    const duplicateQuery = `
      SELECT id FROM system_users 
      WHERE (username = ? OR email = ?) AND id != ?
    `;
    
    const duplicateResult = await executeQuery(duplicateQuery, [username, email, id]);
    
    if (!duplicateResult.success) {
      throw new Error(duplicateResult.error);
    }

    if (duplicateResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Username or email already exists'
      });
    }

    // Update user
    const updateQuery = `
      UPDATE system_users SET
        username = ?, email = ?, first_name = ?, last_name = ?, middle_name = ?,
        phone_number = ?, national_id = ?, date_of_birth = ?, gender = ?,
        address = ?, is_active = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const updateResult = await executeQuery(updateQuery, [
      username, email, first_name, last_name, middle_name,
      phone_number, national_id, date_of_birth, gender, address,
      is_active !== undefined ? is_active : true, id
    ]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    // Get updated user
    const updatedUserQuery = `
      SELECT 
        id, username, email, first_name, last_name, middle_name,
        role, phone_number, national_id, date_of_birth, gender,
        address, is_active, updated_at
      FROM system_users
      WHERE id = ?
    `;

    const updatedUserResult = await executeQuery(updatedUserQuery, [id]);

    res.json({
      success: true,
      message: 'System user updated successfully',
      data: updatedUserResult.data[0]
    });

  } catch (error) {
    console.error('Update system user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update system user'
    });
  }
});

// Delete system user
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if user exists
    const checkQuery = 'SELECT id FROM system_users WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);
    
    if (!checkResult.success) {
      throw new Error(checkResult.error);
    }

    if (checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'System user not found'
      });
    }

    // Prevent deletion of the last admin user
    const adminCountQuery = 'SELECT COUNT(*) as count FROM system_users WHERE role = "system_admin" AND is_active = TRUE';
    const adminCountResult = await executeQuery(adminCountQuery);
    
    if (adminCountResult.success && adminCountResult.data[0].count <= 1) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete the last active system administrator'
      });
    }

    // Delete user
    const deleteQuery = 'DELETE FROM system_users WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'System user deleted successfully'
    });

  } catch (error) {
    console.error('Delete system user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete system user'
    });
  }
});

// Get system user statistics
router.get('/stats/overview', async (req, res) => {
  try {
    const statsQuery = `
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_users,
        COUNT(CASE WHEN is_active = FALSE THEN 1 END) as inactive_users,
        COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent_logins
      FROM system_users
    `;
    
    const result = await executeQuery(statsQuery);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get system user stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve system user statistics'
    });
  }
});

module.exports = router;
