const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');
const { BusinessValidation } = require('../utils/validation');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get all students with optional filters
router.get('/', async (req, res) => {
  try {
    const { class_id, academic_year_id, term_id, status } = req.query;
    
    let query = `
      SELECT 
        s.id, s.admission_number, s.first_name, s.middle_name, s.last_name,
        s.gender, s.status, s.passport_photo, s.created_at, s.updated_at,
        se.class_id, se.academic_year_id, se.term_id, se.enrollment_date,
        c.name as class_name,
        cl.name as class_level_name,
        el.name as education_level_name,
        st.name as stream_name,
        ay.name as academic_year_name,
        t.name as term_name
      FROM students s
      LEFT JOIN student_enrollments se ON s.id = se.student_id
      LEFT JOIN classes c ON se.class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams st ON c.stream_id = st.id
      LEFT JOIN academic_years ay ON se.academic_year_id = ay.id
      LEFT JOIN terms t ON se.term_id = t.id
      WHERE 1=1
    `;
    
    let params = [];
    
    if (class_id) {
      query += ' AND se.class_id = ?';
      params.push(class_id);
    }
    
    if (academic_year_id) {
      query += ' AND se.academic_year_id = ?';
      params.push(academic_year_id);
    }
    
    if (term_id) {
      query += ' AND se.term_id = ?';
      params.push(term_id);
    }
    
    if (status) {
      query += ' AND s.status = ?';
      params.push(status);
    }
    
    query += ' ORDER BY s.admission_number';
    
    const result = await executeQuery(query, params);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get students error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve students'
    });
  }
});

// Get student by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT 
        s.*,
        se.class_id, se.academic_year_id, se.term_id, se.enrollment_date,
        c.name as class_name,
        cl.name as class_level_name,
        el.name as education_level_name,
        st.name as stream_name,
        ay.name as academic_year_name,
        t.name as term_name
      FROM students s
      LEFT JOIN student_enrollments se ON s.id = se.student_id
      LEFT JOIN classes c ON se.class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams st ON c.stream_id = st.id
      LEFT JOIN academic_years ay ON se.academic_year_id = ay.id
      LEFT JOIN terms t ON se.term_id = t.id
      WHERE s.id = ?
    `;
    
    const result = await executeQuery(query, [id]);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    if (result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get student error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve student'
    });
  }
});

// Create new student
router.post('/', async (req, res) => {
  try {
    const {
      admission_number, first_name, middle_name, last_name, gender,
      passport_photo, class_id, academic_year_id, term_id
    } = req.body;

    // Get current user ID for audit fields
    const userId = req.user?.id || 1; // Default to system admin if no user context

    // Add audit fields to data
    const studentData = {
      admission_number, first_name, middle_name, last_name, gender,
      passport_photo, status: 'active',
      created_by_id: userId
    };

    // Validate using business logic
    const validationErrors = await BusinessValidation.validateStudent(studentData, false);
    const auditErrors = BusinessValidation.validateAuditFields(studentData, false);
    const allErrors = [...validationErrors, ...auditErrors];

    if (allErrors.length > 0) {
      return res.status(400).json(BusinessValidation.formatValidationErrors(allErrors));
    }

    // Insert new student
    const insertQuery = `
      INSERT INTO students (
        admission_number, first_name, middle_name, last_name, gender,
        passport_photo, status, created_by_id, updated_by_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, 'active', ?, NULL, NOW(), NOW())
    `;

    const insertResult = await executeQuery(insertQuery, [
      admission_number, first_name, middle_name, last_name, gender, passport_photo, userId
    ]);

    if (!insertResult.success) {
      throw new Error(insertResult.error);
    }

    const studentId = insertResult.data.insertId;

    // If class enrollment data is provided, create enrollment
    if (class_id && academic_year_id && term_id) {
      const enrollmentQuery = `
        INSERT INTO student_enrollments (
          student_id, class_id, academic_year_id, term_id, enrollment_date, enrolled_by_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, NOW(), ?, NOW(), NOW())
      `;

      await executeQuery(enrollmentQuery, [studentId, class_id, academic_year_id, term_id, req.user.id]);
    }

    // Get the created student with enrollment info
    const newStudentQuery = `
      SELECT 
        s.*,
        se.class_id, se.academic_year_id, se.term_id, se.enrollment_date,
        c.name as class_name,
        cl.name as class_level_name,
        el.name as education_level_name,
        ay.name as academic_year_name,
        t.name as term_name
      FROM students s
      LEFT JOIN student_enrollments se ON s.id = se.student_id
      LEFT JOIN classes c ON se.class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN academic_years ay ON se.academic_year_id = ay.id
      LEFT JOIN terms t ON se.term_id = t.id
      WHERE s.id = ?
    `;

    const newStudentResult = await executeQuery(newStudentQuery, [studentId]);

    res.status(201).json({
      success: true,
      message: 'Student created successfully',
      data: newStudentResult.data[0]
    });

  } catch (error) {
    console.error('Create student error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create student'
    });
  }
});

// Update student
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      admission_number, first_name, middle_name, last_name, gender,
      passport_photo, status
    } = req.body;

    // Check if student exists
    const checkQuery = 'SELECT id FROM students WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);
    
    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    // Check if admission number already exists for other students
    const duplicateQuery = 'SELECT id FROM students WHERE admission_number = ? AND id != ?';
    const duplicateResult = await executeQuery(duplicateQuery, [admission_number, id]);
    
    if (!duplicateResult.success) {
      throw new Error(duplicateResult.error);
    }

    if (duplicateResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Admission number already exists'
      });
    }

    // Get current user ID for audit fields
    const userId = req.user?.id || 1; // Default to system admin if no user context

    // Update student
    const updateQuery = `
      UPDATE students SET
        admission_number = ?, first_name = ?, middle_name = ?, last_name = ?,
        gender = ?, passport_photo = ?, status = ?, updated_by_id = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const updateResult = await executeQuery(updateQuery, [
      admission_number, first_name, middle_name, last_name, gender, passport_photo, status, userId, id
    ]);

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    // Get updated student
    const updatedStudentQuery = `
      SELECT 
        s.*,
        se.class_id, se.academic_year_id, se.term_id, se.enrollment_date,
        c.name as class_name,
        cl.name as class_level_name,
        el.name as education_level_name,
        ay.name as academic_year_name,
        t.name as term_name
      FROM students s
      LEFT JOIN student_enrollments se ON s.id = se.student_id
      LEFT JOIN classes c ON se.class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN academic_years ay ON se.academic_year_id = ay.id
      LEFT JOIN terms t ON se.term_id = t.id
      WHERE s.id = ?
    `;

    const updatedStudentResult = await executeQuery(updatedStudentQuery, [id]);

    res.json({
      success: true,
      message: 'Student updated successfully',
      data: updatedStudentResult.data[0]
    });

  } catch (error) {
    console.error('Update student error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update student'
    });
  }
});

// Delete student
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if student exists
    const checkQuery = 'SELECT id FROM students WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    // Delete student (this will cascade to enrollments due to foreign key constraints)
    const deleteQuery = 'DELETE FROM students WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'Student deleted successfully'
    });

  } catch (error) {
    console.error('Delete student error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete student'
    });
  }
});

// Enroll student in class
router.post('/enroll', async (req, res) => {
  try {
    const { student_id, class_id, academic_year_id, term_id } = req.body;

    // Validate required fields
    if (!student_id || !class_id || !academic_year_id || !term_id) {
      return res.status(400).json({
        success: false,
        message: 'Student ID, class ID, academic year ID, and term ID are required'
      });
    }

    // Check if student exists
    const studentQuery = 'SELECT id FROM students WHERE id = ?';
    const studentResult = await executeQuery(studentQuery, [student_id]);

    if (!studentResult.success || studentResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    // Check if enrollment already exists
    const existingQuery = `
      SELECT id FROM student_enrollments
      WHERE student_id = ? AND academic_year_id = ? AND term_id = ?
    `;
    const existingResult = await executeQuery(existingQuery, [student_id, academic_year_id, term_id]);

    if (existingResult.success && existingResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Student is already enrolled for this academic year and term'
      });
    }

    // Create enrollment
    const enrollmentQuery = `
      INSERT INTO student_enrollments (
        student_id, class_id, academic_year_id, term_id, enrollment_date, enrolled_by_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, NOW(), ?, NOW(), NOW())
    `;

    const enrollmentResult = await executeQuery(enrollmentQuery, [student_id, class_id, academic_year_id, term_id, req.user.id]);

    if (!enrollmentResult.success) {
      throw new Error(enrollmentResult.error);
    }

    res.status(201).json({
      success: true,
      message: 'Student enrolled successfully',
      data: {
        enrollment_id: enrollmentResult.data.insertId,
        student_id,
        class_id,
        academic_year_id,
        term_id
      }
    });

  } catch (error) {
    console.error('Enroll student error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to enroll student'
    });
  }
});

// Get all enrollments (for enrollment management)
router.get('/enrollments', async (req, res) => {
  try {
    const { student_id, class_id, academic_year_id, term_id } = req.query;

    let query = `
      SELECT
        se.*,
        s.admission_number, s.first_name, s.middle_name, s.last_name,
        c.name as class_name,
        cl.name as class_level_name,
        el.name as education_level_name,
        st.name as stream_name,
        ay.name as academic_year_name,
        t.name as term_name
      FROM student_enrollments se
      JOIN students s ON se.student_id = s.id
      JOIN classes c ON se.class_id = c.id
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams st ON c.stream_id = st.id
      JOIN academic_years ay ON se.academic_year_id = ay.id
      JOIN terms t ON se.term_id = t.id
      WHERE 1=1
    `;

    let params = [];

    if (student_id) {
      query += ' AND se.student_id = ?';
      params.push(student_id);
    }

    if (class_id) {
      query += ' AND se.class_id = ?';
      params.push(class_id);
    }

    if (academic_year_id) {
      query += ' AND se.academic_year_id = ?';
      params.push(academic_year_id);
    }

    if (term_id) {
      query += ' AND se.term_id = ?';
      params.push(term_id);
    }

    query += ' ORDER BY se.enrollment_date DESC';

    const result = await executeQuery(query, params);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get enrollments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve enrollments'
    });
  }
});

// Delete enrollment
router.delete('/enrollments/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if enrollment exists
    const checkQuery = 'SELECT id, student_id FROM student_enrollments WHERE id = ?';
    const checkResult = await executeQuery(checkQuery, [id]);

    if (!checkResult.success || checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Enrollment not found'
      });
    }

    // Delete enrollment
    const deleteQuery = 'DELETE FROM student_enrollments WHERE id = ?';
    const deleteResult = await executeQuery(deleteQuery, [id]);

    if (!deleteResult.success) {
      throw new Error(deleteResult.error);
    }

    res.json({
      success: true,
      message: 'Enrollment deleted successfully'
    });

  } catch (error) {
    console.error('Delete enrollment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete enrollment'
    });
  }
});

// Get student statistics
router.get('/stats/overview', async (req, res) => {
  try {
    const statsQuery = `
      SELECT
        COUNT(*) as total_students,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_students,
        COUNT(CASE WHEN status = 'transferred' THEN 1 END) as transferred_students,
        COUNT(CASE WHEN status = 'graduated' THEN 1 END) as graduated_students,
        COUNT(CASE WHEN status = 'dropped' THEN 1 END) as dropped_students,
        COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_students,
        COUNT(CASE WHEN gender = 'Male' THEN 1 END) as male_students,
        COUNT(CASE WHEN gender = 'Female' THEN 1 END) as female_students
      FROM students
    `;

    const result = await executeQuery(statsQuery);

    if (!result.success) {
      throw new Error(result.error);
    }

    res.json({
      success: true,
      data: result.data[0]
    });

  } catch (error) {
    console.error('Get student stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve student statistics'
    });
  }
});

// Register new student with enrollment
router.post('/register', async (req, res) => {
  try {
    const {
      admission_number, first_name, middle_name, last_name, gender, passport_photo,
      class_id, academic_year_id, term_id, combination_id, selected_subjects,
      registered_by_id, created_by_id, updated_by_id, enrollment_data
    } = req.body;

    console.log('📝 Registering new student:', { admission_number, first_name, last_name });

    // Validate required fields
    if (!admission_number || !first_name || !last_name || !gender) {
      return res.status(400).json({
        success: false,
        message: 'Admission number, first name, last name, and gender are required'
      });
    }

    // Check if admission number already exists
    const checkQuery = 'SELECT id FROM students WHERE admission_number = ?';
    const checkResult = await executeQuery(checkQuery, [admission_number]);

    if (!checkResult.success) {
      throw new Error(checkResult.error);
    }

    if (checkResult.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Admission number already exists'
      });
    }

    // Start transaction for student registration and enrollment
    const connection = await require('../../database/connection').pool.getConnection();

    try {
      await connection.beginTransaction();

      // Get current user ID for audit fields
      const userId = req.user?.id || 1; // Default to system admin if no user context

      // Insert new student
      const insertStudentQuery = `
        INSERT INTO students (
          admission_number, first_name, middle_name, last_name, gender,
          passport_photo, status, created_by_id, updated_by_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, 'active', ?, NULL, NOW(), NOW())
      `;

      const [studentResult] = await connection.execute(insertStudentQuery, [
        admission_number, first_name, middle_name, last_name, gender, passport_photo, userId
      ]);

      const studentId = studentResult.insertId;
      console.log('✅ Student created with ID:', studentId);

      // Create enrollment if enrollment data is provided
      if (class_id && academic_year_id && term_id) {
        const enrollmentQuery = `
          INSERT INTO student_enrollments (
            student_id, class_id, academic_year_id, term_id, combination_id,
            enrollment_date, enrolled_by_id, status, enrollment_notes,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, NOW(), ?, 'active', ?, NOW(), NOW())
        `;

        const enrolledBy = registered_by_id || created_by_id || userId;
        const enrollmentNotes = enrollment_data?.enrollment_notes || `Student registered on ${new Date().toLocaleDateString()}`;

        await connection.execute(enrollmentQuery, [
          studentId, class_id, academic_year_id, term_id, combination_id || null, enrolledBy, enrollmentNotes
        ]);

        console.log('✅ Student enrolled successfully in class:', class_id);
      }

      // Handle subject selections for A-Level students
      if (selected_subjects && Array.isArray(selected_subjects) && selected_subjects.length > 0) {
        // Get class level type to determine subject table
        const classQuery = `
          SELECT el.code as education_level_code, cl.id as class_level_id
          FROM classes c
          JOIN class_levels cl ON c.class_level_id = cl.id
          JOIN education_levels el ON cl.education_level_id = el.id
          WHERE c.id = ?
        `;
        const [classResult] = await connection.execute(classQuery, [class_id]);

        if (classResult.length > 0 && classResult[0].education_level_code === 'a_level') {
          const classLevelId = classResult[0].class_level_id;

          // Insert A-Level student subjects
          const subjectInsertQuery = `
            INSERT INTO a_level_student_subjects (
              student_id, subject_id, class_level_id, is_active,
              created_at, updated_at
            ) VALUES (?, ?, ?, TRUE, NOW(), NOW())
          `;

          for (const subjectId of selected_subjects) {
            await connection.execute(subjectInsertQuery, [studentId, subjectId, classLevelId]);
          }
          console.log('✅ A-Level subjects assigned:', selected_subjects.length, 'subjects');
        } else if (classResult.length > 0 && classResult[0].education_level_code === 'o_level') {
          const classLevelId = classResult[0].class_level_id;

          // Insert O-Level student subjects
          const subjectInsertQuery = `
            INSERT INTO o_level_student_subjects (
              student_id, subject_id, selection_type, class_level_id,
              selection_date, is_active, created_at, updated_at
            ) VALUES (?, ?, 'elective', ?, CURDATE(), TRUE, NOW(), NOW())
          `;

          for (const subjectId of selected_subjects) {
            await connection.execute(subjectInsertQuery, [studentId, subjectId, classLevelId]);
          }
          console.log('✅ O-Level subjects assigned:', selected_subjects.length, 'subjects');
        }
      }

      await connection.commit();

      // Get the complete student record with enrollment info
      const studentDataQuery = `
        SELECT
          s.*,
          se.class_id, se.academic_year_id, se.term_id, se.combination_id, se.enrollment_date,
          c.name as class_name,
          l.name as level_name, l.level_type,
          st.name as stream_name,
          ay.name as academic_year_name,
          t.name as term_name,
          alc.name as combination_name
        FROM students s
        LEFT JOIN student_enrollments se ON s.id = se.student_id
        LEFT JOIN classes c ON se.class_id = c.id
        LEFT JOIN class_levels cl ON c.class_level_id = cl.id
        LEFT JOIN education_levels el ON cl.education_level_id = el.id
        LEFT JOIN streams st ON c.stream_id = st.id
        LEFT JOIN academic_years ay ON se.academic_year_id = ay.id
        LEFT JOIN terms t ON se.term_id = t.id
        LEFT JOIN a_level_combinations alc ON se.combination_id = alc.id
        WHERE s.id = ?
      `;

      const studentDataResult = await executeQuery(studentDataQuery, [studentId]);

      res.status(201).json({
        success: true,
        message: 'Student registered successfully',
        data: studentDataResult.data[0]
      });

    } catch (transactionError) {
      await connection.rollback();
      throw transactionError;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('❌ Student registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to register student',
      error: error.message
    });
  }
});

// Get registration form data (classes, academic years, terms)
router.get('/registration-form-data', async (req, res) => {
  try {
    console.log('📋 Fetching registration form data...');

    // Get class levels first
    const classLevelsQuery = `
      SELECT cl.id, cl.code, cl.name, cl.display_name, cl.sort_order, cl.streams_optional,
             el.code as education_level_code, el.name as education_level_name
      FROM class_levels cl
      JOIN education_levels el ON cl.education_level_id = el.id
      WHERE cl.is_active = 1
      ORDER BY cl.sort_order
    `;

    // Get streams
    const streamsQuery = `
      SELECT id, name, display_name, stream_type, academic_year_id
      FROM streams
      WHERE is_active = 1
      ORDER BY stream_type, name
    `;

    // Get classes with class level information
    const classesQuery = `
      SELECT
        c.id,
        c.name as class_name,
        c.class_level_id,
        c.stream_id,
        c.current_enrollment,
        cl.name as class_level_name,
        cl.display_name as class_level_display_name,
        el.code as education_level_code,
        el.name as education_level_name,
        s.name as stream_name,
        s.display_name as stream_display_name
      FROM classes c
      LEFT JOIN class_levels cl ON c.class_level_id = cl.id
      LEFT JOIN education_levels el ON cl.education_level_id = el.id
      LEFT JOIN streams s ON c.stream_id = s.id
      WHERE c.is_active = 1
      ORDER BY cl.sort_order, c.name
    `;

    // Get academic years
    const academicYearsQuery = `
      SELECT id, name, start_date, end_date, is_active
      FROM academic_years
      ORDER BY name DESC
    `;

    // Get terms
    const termsQuery = `
      SELECT id, name, number, start_date, end_date, is_active, academic_year_id
      FROM terms
      ORDER BY academic_year_id, number
    `;

    // Execute all queries
    const [classLevelsResult, streamsResult, classesResult, academicYearsResult, termsResult] = await Promise.all([
      executeQuery(classLevelsQuery),
      executeQuery(streamsQuery),
      executeQuery(classesQuery),
      executeQuery(academicYearsQuery),
      executeQuery(termsQuery)
    ]);

    // Check if all queries succeeded
    if (!classLevelsResult.success || !streamsResult.success || !classesResult.success || !academicYearsResult.success || !termsResult.success) {
      console.error('❌ One or more queries failed:', {
        classLevels: classLevelsResult.success,
        streams: streamsResult.success,
        classes: classesResult.success,
        academicYears: academicYearsResult.success,
        terms: termsResult.success
      });
      throw new Error('Failed to fetch form data');
    }

    console.log('✅ Registration form data fetched successfully:', {
      classLevels: classLevelsResult.data.length,
      streams: streamsResult.data.length,
      classes: classesResult.data.length,
      academicYears: academicYearsResult.data.length,
      terms: termsResult.data.length
    });

    res.json({
      success: true,
      data: {
        class_levels: classLevelsResult.data,
        streams: streamsResult.data,
        classes: classesResult.data,
        academic_years: academicYearsResult.data,
        terms: termsResult.data
      }
    });

  } catch (error) {
    console.error('❌ Get registration form data error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve registration form data',
      error: error.message
    });
  }
});

module.exports = router;
