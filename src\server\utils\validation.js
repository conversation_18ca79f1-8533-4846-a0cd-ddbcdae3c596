/**
 * Business Logic Validation Module
 * Centralized validation logic for all business entities
 */

const { executeQuery } = require('../../database/connection');

class ValidationError extends Error {
  constructor(message, field = null, code = null) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
    this.code = code;
  }
}

const BusinessValidation = {
  /**
   * Validate academic year data
   */
  async validateAcademicYear(data, isUpdate = false, existingId = null) {
    const errors = [];

    // Required fields
    if (!data.name) {
      errors.push(new ValidationError('Academic year name is required', 'name', 'REQUIRED'));
    }

    if (!data.start_date) {
      errors.push(new ValidationError('Start date is required', 'start_date', 'REQUIRED'));
    }

    if (!data.end_date) {
      errors.push(new ValidationError('End date is required', 'end_date', 'REQUIRED'));
    }

    // Date validation
    if (data.start_date && data.end_date) {
      const startDate = new Date(data.start_date);
      const endDate = new Date(data.end_date);

      if (startDate >= endDate) {
        errors.push(new ValidationError('End date must be after start date', 'end_date', 'INVALID_DATE_RANGE'));
      }
    }

    // Unique name validation
    if (data.name) {
      const nameQuery = isUpdate 
        ? 'SELECT id FROM academic_years WHERE name = ? AND id != ?'
        : 'SELECT id FROM academic_years WHERE name = ?';
      const nameParams = isUpdate ? [data.name, existingId] : [data.name];
      
      const nameResult = await executeQuery(nameQuery, nameParams);
      if (nameResult.success && nameResult.data.length > 0) {
        errors.push(new ValidationError('Academic year name already exists', 'name', 'DUPLICATE'));
      }
    }

    // Only one active academic year allowed
    if (data.is_active) {
      const activeQuery = isUpdate 
        ? 'SELECT id FROM academic_years WHERE is_active = TRUE AND id != ?'
        : 'SELECT id FROM academic_years WHERE is_active = TRUE';
      const activeParams = isUpdate ? [existingId] : [];
      
      const activeResult = await executeQuery(activeQuery, activeParams);
      if (activeResult.success && activeResult.data.length > 0) {
        // This is handled by the business logic, not an error
        // The system will automatically deactivate other years
      }
    }

    return errors;
  },

  /**
   * Validate term data
   */
  async validateTerm(data, isUpdate = false, existingId = null) {
    const errors = [];

    // Required fields
    if (!data.name) {
      errors.push(new ValidationError('Term name is required', 'name', 'REQUIRED'));
    }

    if (!data.number) {
      errors.push(new ValidationError('Term number is required', 'number', 'REQUIRED'));
    }

    if (!data.academic_year_id) {
      errors.push(new ValidationError('Academic year is required', 'academic_year_id', 'REQUIRED'));
    }

    if (!data.start_date) {
      errors.push(new ValidationError('Start date is required', 'start_date', 'REQUIRED'));
    }

    if (!data.end_date) {
      errors.push(new ValidationError('End date is required', 'end_date', 'REQUIRED'));
    }

    // Term number validation (1, 2, or 3)
    if (data.number && (data.number < 1 || data.number > 3)) {
      errors.push(new ValidationError('Term number must be 1, 2, or 3', 'number', 'INVALID_RANGE'));
    }

    // Date validation
    if (data.start_date && data.end_date) {
      const startDate = new Date(data.start_date);
      const endDate = new Date(data.end_date);

      if (startDate >= endDate) {
        errors.push(new ValidationError('End date must be after start date', 'end_date', 'INVALID_DATE_RANGE'));
      }
    }

    // Academic year exists validation
    if (data.academic_year_id) {
      const yearQuery = 'SELECT id FROM academic_years WHERE id = ?';
      const yearResult = await executeQuery(yearQuery, [data.academic_year_id]);
      if (!yearResult.success || yearResult.data.length === 0) {
        errors.push(new ValidationError('Academic year does not exist', 'academic_year_id', 'NOT_FOUND'));
      }
    }

    // Unique term number per academic year
    if (data.academic_year_id && data.number) {
      const uniqueQuery = isUpdate 
        ? 'SELECT id FROM terms WHERE academic_year_id = ? AND number = ? AND id != ?'
        : 'SELECT id FROM terms WHERE academic_year_id = ? AND number = ?';
      const uniqueParams = isUpdate 
        ? [data.academic_year_id, data.number, existingId] 
        : [data.academic_year_id, data.number];
      
      const uniqueResult = await executeQuery(uniqueQuery, uniqueParams);
      if (uniqueResult.success && uniqueResult.data.length > 0) {
        errors.push(new ValidationError('Term number already exists for this academic year', 'number', 'DUPLICATE'));
      }
    }

    return errors;
  },

  /**
   * Validate subject selection rules
   */
  async validateSubjectSelection(data) {
    const errors = [];

    // Validate O-Level subject selection
    if (data.subject_level === 'o_level') {
      // Check if subject exists in o_level_subjects
      const subjectQuery = 'SELECT id FROM o_level_subjects WHERE id = ?';
      const subjectResult = await executeQuery(subjectQuery, [data.subject_id]);
      if (!subjectResult.success || subjectResult.data.length === 0) {
        errors.push(new ValidationError('O-Level subject does not exist', 'subject_id', 'NOT_FOUND'));
      }
    }

    // Validate A-Level subject selection
    if (data.subject_level === 'a_level') {
      // Check if subject exists in a_level_subjects
      const subjectQuery = 'SELECT id FROM a_level_subjects WHERE id = ?';
      const subjectResult = await executeQuery(subjectQuery, [data.subject_id]);
      if (!subjectResult.success || subjectResult.data.length === 0) {
        errors.push(new ValidationError('A-Level subject does not exist', 'subject_id', 'NOT_FOUND'));
      }
    }

    return errors;
  },

  /**
   * Validate competency scores
   */
  validateCompetencyScore(score) {
    const errors = [];

    if (score < 1 || score > 3) {
      errors.push(new ValidationError('Competency score must be between 1 and 3', 'competency_score', 'INVALID_RANGE'));
    }

    return errors;
  },

  /**
   * Validate exam marks and percentages
   */
  validateExamMarks(data) {
    const errors = [];

    if (data.marks_obtained !== undefined && data.total_marks !== undefined) {
      if (data.marks_obtained < 0 || data.marks_obtained > data.total_marks) {
        errors.push(new ValidationError('Marks obtained must be between 0 and total marks', 'marks_obtained', 'INVALID_RANGE'));
      }
    }

    if (data.percentage !== undefined) {
      if (data.percentage < 0 || data.percentage > 100) {
        errors.push(new ValidationError('Percentage must be between 0 and 100', 'percentage', 'INVALID_RANGE'));
      }
    }

    if (data.grade_points !== undefined) {
      if (data.grade_points < 0 || data.grade_points > 1) {
        errors.push(new ValidationError('Grade points must be between 0 and 1', 'grade_points', 'INVALID_RANGE'));
      }
    }

    return errors;
  },

  /**
   * Validate teacher data
   */
  async validateTeacher(data, isUpdate = false, existingId = null) {
    const errors = [];

    // Required fields
    if (!data.first_name) {
      errors.push(new ValidationError('First name is required', 'first_name', 'REQUIRED'));
    }

    if (!data.last_name) {
      errors.push(new ValidationError('Last name is required', 'last_name', 'REQUIRED'));
    }

    if (!data.teacher_type) {
      errors.push(new ValidationError('Teacher type is required', 'teacher_type', 'REQUIRED'));
    }

    if (!data.employment_status) {
      errors.push(new ValidationError('Employment status is required', 'employment_status', 'REQUIRED'));
    }

    if (!data.academic_year_id) {
      errors.push(new ValidationError('Academic year is required', 'academic_year_id', 'REQUIRED'));
    }

    // Teacher type validation
    const validTeacherTypes = ['Class Teacher', 'Subject Teacher'];
    if (data.teacher_type && !validTeacherTypes.includes(data.teacher_type)) {
      errors.push(new ValidationError('Invalid teacher type', 'teacher_type', 'INVALID_VALUE'));
    }

    // Employment status validation
    const validEmploymentStatuses = ['active', 'inactive', 'terminated', 'retired'];
    if (data.employment_status && !validEmploymentStatuses.includes(data.employment_status)) {
      errors.push(new ValidationError('Invalid employment status', 'employment_status', 'INVALID_VALUE'));
    }

    // Academic year exists validation
    if (data.academic_year_id) {
      const yearQuery = 'SELECT id FROM academic_years WHERE id = ?';
      const yearResult = await executeQuery(yearQuery, [data.academic_year_id]);
      if (!yearResult.success || yearResult.data.length === 0) {
        errors.push(new ValidationError('Academic year does not exist', 'academic_year_id', 'NOT_FOUND'));
      }
    }

    // Name length validation
    if (data.first_name && data.first_name.length > 50) {
      errors.push(new ValidationError('First name must be 50 characters or less', 'first_name', 'TOO_LONG'));
    }

    if (data.last_name && data.last_name.length > 50) {
      errors.push(new ValidationError('Last name must be 50 characters or less', 'last_name', 'TOO_LONG'));
    }

    if (data.middle_name && data.middle_name.length > 50) {
      errors.push(new ValidationError('Middle name must be 50 characters or less', 'middle_name', 'TOO_LONG'));
    }

    return errors;
  },

  /**
   * Validate student data
   */
  async validateStudent(data, isUpdate = false, existingId = null) {
    const errors = [];

    // Required fields
    if (!data.admission_number) {
      errors.push(new ValidationError('Admission number is required', 'admission_number', 'REQUIRED'));
    }

    if (!data.first_name) {
      errors.push(new ValidationError('First name is required', 'first_name', 'REQUIRED'));
    }

    if (!data.last_name) {
      errors.push(new ValidationError('Last name is required', 'last_name', 'REQUIRED'));
    }

    if (!data.gender) {
      errors.push(new ValidationError('Gender is required', 'gender', 'REQUIRED'));
    }

    // Gender validation
    const validGenders = ['Male', 'Female'];
    if (data.gender && !validGenders.includes(data.gender)) {
      errors.push(new ValidationError('Invalid gender', 'gender', 'INVALID_VALUE'));
    }

    // Status validation
    const validStatuses = ['active', 'transferred', 'graduated', 'dropped', 'suspended'];
    if (data.status && !validStatuses.includes(data.status)) {
      errors.push(new ValidationError('Invalid status', 'status', 'INVALID_VALUE'));
    }

    // Unique admission number validation
    if (data.admission_number) {
      const admissionQuery = isUpdate 
        ? 'SELECT id FROM students WHERE admission_number = ? AND id != ?'
        : 'SELECT id FROM students WHERE admission_number = ?';
      const admissionParams = isUpdate ? [data.admission_number, existingId] : [data.admission_number];
      
      const admissionResult = await executeQuery(admissionQuery, admissionParams);
      if (admissionResult.success && admissionResult.data.length > 0) {
        errors.push(new ValidationError('Admission number already exists', 'admission_number', 'DUPLICATE'));
      }
    }

    return errors;
  },

  /**
   * Validate audit fields
   */
  validateAuditFields(data, isUpdate = false) {
    const errors = [];

    if (!isUpdate && !data.created_by_id) {
      errors.push(new ValidationError('Created by ID is required for new records', 'created_by_id', 'REQUIRED'));
    }

    if (isUpdate && !data.updated_by_id) {
      errors.push(new ValidationError('Updated by ID is required for updates', 'updated_by_id', 'REQUIRED'));
    }

    return errors;
  },

  /**
   * Format validation errors for API response
   */
  formatValidationErrors(errors) {
    return {
      success: false,
      message: 'Validation failed',
      errors: errors.map(error => ({
        field: error.field,
        message: error.message,
        code: error.code
      }))
    };
  }
};

module.exports = {
  BusinessValidation,
  ValidationError
};
