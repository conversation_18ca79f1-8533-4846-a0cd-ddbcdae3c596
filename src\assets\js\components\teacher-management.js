// AIMS Teacher Management Components
// Comprehensive teacher management system with registration and management

// Uses global API services: window.TeachersAPI, window.SubjectsAPI, window.AcademicYearsAPI
// Uses global config: window.AIMSConfig
// Uses environment configuration: ../config/environment.js

const TeacherManagementComponents = {
  // Component state
  state: {
    teachers: [],
    subjects: [],
    academicYears: [],
    loading: false,
    filters: {
      teacherType: '',
      employmentStatus: '',
      search: ''
    }
  },

  // Initialize component
  async init() {
    await this.loadInitialData();
  },

  // Load initial data
  async loadInitialData() {
    try {
      this.state.loading = true;

      console.log('🔄 Loading teacher management data...');

      // Use the API services with proper error handling
      const [teachers, subjects, academicYears] = await Promise.all([
        window.TeachersAPI.getAll(),
        window.SubjectsAPI.getAll(),
        window.AcademicYearsAPI.getAll()
      ]);

      this.state.teachers = teachers;
      this.state.subjects = subjects;
      this.state.academicYears = academicYears;

      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.log('✅ Teacher management data loaded:', { teachers, subjects, academicYears });
      }

    } catch (error) {
      console.error('❌ Failed to load initial data:', error);
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.error('Debug: Teacher data loading error details:', error);
      }
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to load data', 'error');
      }
    } finally {
      this.state.loading = false;
    }
  }
};

// Register Teacher Component
const RegisterTeacherComponent = {
  // Render register teacher form
  render() {
    return `
      <div class="space-y-6">
        ${AIMSDesignSystem.layouts.pageHeader(
          'Register Teacher',
          'Add new teaching staff to the system with their qualifications and subject assignments'
        )}

        <!-- Registration Form -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <form id="register-teacher-form" class="space-y-6">
            <!-- Hidden fields for system admin tracking -->
            ${window.AuditFieldsUtil ? window.AuditFieldsUtil.createAuditFieldsHTML(true) : `
              <input type="hidden" id="created_by_id" name="created_by_id" value="">
              <input type="hidden" id="updated_by_id" name="updated_by_id" value="">
            `}

            <!-- Personal Information Section -->
            <div>
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                ${AIMSDesignSystem.forms.input('first_name', 'First Name', '', {
                  required: true,
                  placeholder: 'Enter first name'
                })}
                ${AIMSDesignSystem.forms.input('middle_name', 'Middle Name', '', {
                  placeholder: 'Enter middle name (optional)'
                })}
                ${AIMSDesignSystem.forms.input('last_name', 'Last Name', '', {
                  required: true,
                  placeholder: 'Enter last name'
                })}
                ${AIMSDesignSystem.forms.input('initials', 'Initials', '', {
                  placeholder: 'e.g., M.N.',
                  helpText: 'Teacher initials for display purposes'
                })}
                ${AIMSDesignSystem.forms.fileUpload('profile_picture', 'Profile Picture', {
                  accept: 'image/*',
                  helpText: 'Upload teacher profile picture (optional)'
                })}
              </div>
            </div>

            <!-- Professional Information Section -->
            <div>
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Professional Information</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                ${AIMSDesignSystem.forms.select('teacher_type', 'Teacher Type', [
                  { value: '', label: 'Select Teacher Type' },
                  { value: 'Class Teacher', label: 'Class Teacher' },
                  { value: 'Subject Teacher', label: 'Subject Teacher' }
                ], 'Subject Teacher', { required: true })}
                ${AIMSDesignSystem.forms.select('employment_status', 'Employment Status', [
                  { value: '', label: 'Select Employment Status' },
                  { value: 'active', label: 'Active' },
                  { value: 'inactive', label: 'Inactive' },
                  { value: 'terminated', label: 'Terminated' },
                  { value: 'retired', label: 'Retired' }
                ], 'active', { required: true })}
                ${AIMSDesignSystem.forms.input('joining_date', 'Joining Date', '', {
                  type: 'date',
                  required: true
                })}
                ${AIMSDesignSystem.forms.select('academic_year_id', 'Academic Year', [], '', { required: true })}
              </div>
            </div>

            <!-- Subject Assignments Section -->
            <div>
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Subject Assignments</h3>
              <div id="subject-assignments-container">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <!-- Subject checkboxes will be populated here -->
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
              ${AIMSDesignSystem.forms.button('cancel', 'Cancel', 'secondary', {
                type: 'button',
                onclick: 'RegisterTeacherComponent.cancel()'
              })}
              ${AIMSDesignSystem.forms.button('register', 'Register Teacher', 'primary', {
                type: 'submit',
                loading: false
              })}
            </div>
          </form>
        </div>
      </div>
    `;
  },

  // Initialize register teacher component
  async init() {
    await TeacherManagementComponents.loadInitialData();
    this.populateDropdowns();
    this.populateSubjects();
    this.populateSystemAdminFields();
    this.initializeEventListeners();
  },

  // Populate system admin fields with current logged-in admin ID
  populateSystemAdminFields() {
    // Use the audit fields utility for consistent behavior
    if (window.AuditFieldsUtil) {
      window.AuditFieldsUtil.populateAuditFields('register-teacher-form', false);
    } else {
      // Fallback if utility is not loaded
      const currentAdminId = this.getCurrentAdminId();
      if (currentAdminId) {
        const createdByField = document.getElementById('teacher_created_by_id');
        if (createdByField) createdByField.value = currentAdminId;
      }
    }
  },

  // Get current admin ID from authenticated user (fallback method)
  getCurrentAdminId() {
    // Use the audit fields utility if available
    if (window.AuditFieldsUtil) {
      return window.AuditFieldsUtil.getCurrentUserId();
    }

    // Fallback implementation
    try {
      if (window.AIMS && window.AIMS.currentUser && window.AIMS.currentUser.id) {
        return window.AIMS.currentUser.id;
      }
      return 1;
    } catch (error) {
      console.error('Error getting current admin ID:', error);
      return 1;
    }
  },

  // Populate dropdown fields
  populateDropdowns() {
    // Populate academic years
    const academicYearSelect = document.getElementById('academic_year_id');
    if (academicYearSelect) {
      academicYearSelect.innerHTML = '<option value="">Select Academic Year</option>';
      const academicYears = TeacherManagementComponents.state.academicYears.data || TeacherManagementComponents.state.academicYears;
      if (Array.isArray(academicYears)) {
        academicYears.forEach(year => {
          const option = document.createElement('option');
          option.value = year.id;
          option.textContent = year.name;
          if (year.is_active) option.selected = true;
          academicYearSelect.appendChild(option);
        });
      }
    }
  },

  // Populate subjects for assignment
  populateSubjects() {
    const container = document.getElementById('subject-assignments-container');
    if (!container) return;

    const subjectsData = TeacherManagementComponents.state.subjects.data || TeacherManagementComponents.state.subjects;
    const subjects = Array.isArray(subjectsData) ? subjectsData : [];
    
    container.innerHTML = `
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        ${subjects.map(subject => `
          <div class="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
            <input type="checkbox" 
                   id="subject_${subject.id}" 
                   name="teaching_subjects[]" 
                   value="${subject.id}"
                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="subject_${subject.id}" class="flex-1">
              <div class="font-medium text-gray-900">${subject.name}</div>
              <div class="text-sm text-gray-500">${subject.short_name} - ${subject.level.replace('_', '-').toUpperCase()}</div>
            </label>
          </div>
        `).join('')}
      </div>
      <div class="mt-4 p-3 bg-blue-50 rounded-lg">
        <p class="text-sm text-blue-700">
          <i class="fas fa-info-circle mr-2"></i>
          Select the subjects this teacher will be qualified to teach. You can modify these assignments later.
        </p>
      </div>
    `;
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Handle form submission
    const form = document.getElementById('register-teacher-form');
    if (form) {
      form.addEventListener('submit', this.handleSubmit.bind(this));
    }
  },

  // Handle form submission
  async handleSubmit(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    let data = Object.fromEntries(formData.entries());

    // Add audit fields using the utility (these are also handled by the backend from JWT token)
    if (window.AuditFieldsUtil) {
      data = window.AuditFieldsUtil.addAuditFieldsToData(data, false); // false = new record
    } else {
      // Fallback
      const currentAdminId = this.getCurrentAdminId();
      if (currentAdminId) {
        data.created_by_id = currentAdminId;
      }
    }

    // Collect selected subjects
    const teachingSubjects = Array.from(document.querySelectorAll('input[name="teaching_subjects[]"]:checked'))
      .map(input => parseInt(input.value));

    data.teaching_subjects = JSON.stringify(teachingSubjects);

    try {
      AIMSDesignSystem.forms.setButtonLoading('register', true);

      // Use the API service
      const result = await window.TeachersAPI.create(data);

      if (result.success) {
        // Show success notification if available
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show('Teacher registered successfully!', 'success');
        }
        event.target.reset();
        // Optionally redirect to manage teachers
        setTimeout(() => {
          if (window.PageRouter) {
            window.PageRouter.loadPage('manage-teachers');
          }
        }, 1500);
      } else {
        // Show error notification if available
        if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
          window.AIMSDesignSystem.notifications.show(result.message || 'Failed to register teacher', 'error');
        }
      }
    } catch (error) {
      console.error('Registration error:', error);
      // Show error notification if available
      if (window.AIMSDesignSystem && window.AIMSDesignSystem.notifications) {
        window.AIMSDesignSystem.notifications.show('Failed to register teacher', 'error');
      }
    } finally {
      AIMSDesignSystem.forms.setButtonLoading('register', false);
    }
  },

  // Cancel registration
  cancel() {
    if (confirm('Are you sure you want to cancel? All entered data will be lost.')) {
      if (window.PageRouter) {
        window.PageRouter.loadPage('manage-teachers');
      }
    }
  }
};

// Manage Teachers Component
const ManageTeachersComponent = {
  // Render manage teachers interface
  render() {
    return `
      <div class="space-y-6">
        ${AIMSDesignSystem.layouts.pageHeader(
          'Manage Teachers',
          'View, edit, and manage teaching staff records and assignments',
          [
            { icon: 'fas fa-chalkboard-teacher', value: TeacherManagementComponents.state.teachers.data?.length || 0, label: 'Total Teachers', color: 'blue' },
            { icon: 'fas fa-user-check', value: this.getActiveTeachersCount(), label: 'Active', color: 'green' }
          ]
        )}

        <!-- Filters and Search -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 p-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            ${AIMSDesignSystem.forms.input('search', 'Search Teachers', '', {
              placeholder: 'Search by name or initials...',
              icon: 'fas fa-search'
            })}
            ${AIMSDesignSystem.forms.select('filter_teacher_type', 'Filter by Type', [
              { value: '', label: 'All Types' },
              { value: 'Class Teacher', label: 'Class Teacher' },
              { value: 'Subject Teacher', label: 'Subject Teacher' }
            ], '')}
            ${AIMSDesignSystem.forms.select('filter_employment_status', 'Filter by Status', [
              { value: '', label: 'All Status' },
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' },
              { value: 'terminated', label: 'Terminated' },
              { value: 'retired', label: 'Retired' }
            ], '')}
            <div class="flex items-end space-x-2">
              ${AIMSDesignSystem.forms.button('add-teacher', 'Add Teacher', 'primary', {
                icon: 'fas fa-plus',
                onclick: 'ManageTeachersComponent.addTeacher()'
              })}
              ${AIMSDesignSystem.forms.button('export', 'Export', 'secondary', {
                icon: 'fas fa-download',
                onclick: 'ManageTeachersComponent.exportTeachers()'
              })}
            </div>
          </div>
        </div>

        <!-- Teachers Table -->
        <div class="bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Teaching Staff Records</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Initials</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subjects</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joining Date</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody id="teachers-table-body" class="bg-white divide-y divide-gray-200">
                <!-- Teachers will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  },

  // Initialize manage teachers component
  async init() {
    await TeacherManagementComponents.loadInitialData();
    this.populateTeachersTable();
    this.initializeEventListeners();
  },

  // Get active teachers count
  getActiveTeachersCount() {
    const teachers = TeacherManagementComponents.state.teachers.data || TeacherManagementComponents.state.teachers;
    return Array.isArray(teachers) ? teachers.filter(t => t.employment_status === 'active').length : 0;
  },

  // Populate teachers table
  populateTeachersTable() {
    const tbody = document.getElementById('teachers-table-body');
    if (!tbody) return;

    const teachers = this.getFilteredTeachers();

    if (teachers.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="7" class="px-6 py-12 text-center text-gray-500">
            <i class="fas fa-chalkboard-teacher text-4xl mb-4 text-gray-300"></i>
            <p class="text-lg font-medium">No teachers found</p>
            <p class="text-sm">Try adjusting your search criteria or add new teachers.</p>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = teachers.map(teacher => `
      <tr class="hover:bg-gray-50">
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <div class="flex-shrink-0 h-10 w-10">
              <img class="h-10 w-10 rounded-full object-cover"
                   src="${teacher.profile_picture || '/assets/images/default-avatar.png'}"
                   alt="${teacher.first_name} ${teacher.last_name}">
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-gray-900">
                ${teacher.first_name} ${teacher.middle_name || ''} ${teacher.last_name}
              </div>
              <div class="text-sm text-gray-500">${teacher.teacher_type}</div>
            </div>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${teacher.initials || '-'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          ${teacher.teacher_type}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusBadgeClass(teacher.employment_status)}">
            ${teacher.employment_status}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${this.getTeachingSubjects(teacher.teaching_subjects)}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${teacher.joining_date ? new Date(teacher.joining_date).toLocaleDateString() : '-'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div class="flex items-center justify-end space-x-2">
            <button onclick="ManageTeachersComponent.viewTeacher(${teacher.id})"
                    class="text-blue-600 hover:text-blue-900" title="View Details">
              <i class="fas fa-eye"></i>
            </button>
            <button onclick="ManageTeachersComponent.editTeacher(${teacher.id})"
                    class="text-indigo-600 hover:text-indigo-900" title="Edit">
              <i class="fas fa-edit"></i>
            </button>
            <button onclick="ManageTeachersComponent.assignSubjects(${teacher.id})"
                    class="text-green-600 hover:text-green-900" title="Assign Subjects">
              <i class="fas fa-book"></i>
            </button>
            <button onclick="ManageTeachersComponent.deleteTeacher(${teacher.id})"
                    class="text-red-600 hover:text-red-900" title="Delete">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </td>
      </tr>
    `).join('');
  },

  // Get filtered teachers based on search and filters
  getFilteredTeachers() {
    const teachersData = TeacherManagementComponents.state.teachers.data || TeacherManagementComponents.state.teachers;
    let teachers = Array.isArray(teachersData) ? teachersData : [];

    // Apply search filter
    const searchTerm = document.getElementById('search')?.value.toLowerCase() || '';
    if (searchTerm) {
      teachers = teachers.filter(teacher =>
        teacher.first_name.toLowerCase().includes(searchTerm) ||
        teacher.last_name.toLowerCase().includes(searchTerm) ||
        (teacher.initials && teacher.initials.toLowerCase().includes(searchTerm))
      );
    }

    // Apply teacher type filter
    const typeFilter = document.getElementById('filter_teacher_type')?.value || '';
    if (typeFilter) {
      teachers = teachers.filter(teacher => teacher.teacher_type === typeFilter);
    }

    // Apply employment status filter
    const statusFilter = document.getElementById('filter_employment_status')?.value || '';
    if (statusFilter) {
      teachers = teachers.filter(teacher => teacher.employment_status === statusFilter);
    }

    return teachers;
  },

  // Get status badge CSS class
  getStatusBadgeClass(status) {
    const classes = {
      'active': 'bg-green-100 text-green-800',
      'inactive': 'bg-yellow-100 text-yellow-800',
      'terminated': 'bg-red-100 text-red-800',
      'retired': 'bg-purple-100 text-purple-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
  },

  // Get teaching subjects display
  getTeachingSubjects(teachingSubjectsJson) {
    if (!teachingSubjectsJson) return '-';

    try {
      const subjectIds = JSON.parse(teachingSubjectsJson);
      const subjects = TeacherManagementComponents.state.subjects.data || [];
      const teachingSubjects = subjects.filter(s => subjectIds.includes(s.id));

      if (teachingSubjects.length === 0) return '-';
      if (teachingSubjects.length <= 2) {
        return teachingSubjects.map(s => s.short_name).join(', ');
      }
      return `${teachingSubjects.slice(0, 2).map(s => s.short_name).join(', ')} +${teachingSubjects.length - 2}`;
    } catch (error) {
      return '-';
    }
  },

  // Initialize event listeners
  initializeEventListeners() {
    // Search input
    const searchInput = document.getElementById('search');
    if (searchInput) {
      searchInput.addEventListener('input', () => {
        this.populateTeachersTable();
      });
    }

    // Filter dropdowns
    ['filter_teacher_type', 'filter_employment_status'].forEach(filterId => {
      const filterElement = document.getElementById(filterId);
      if (filterElement) {
        filterElement.addEventListener('change', () => {
          this.populateTeachersTable();
        });
      }
    });
  },

  // Add new teacher
  addTeacher() {
    if (window.PageRouter) {
      window.PageRouter.loadPage('register-teacher');
    }
  },

  // View teacher details
  viewTeacher(teacherId) {
    console.log('View teacher:', teacherId);
  },

  // Edit teacher
  editTeacher(teacherId) {
    console.log('Edit teacher:', teacherId);
  },

  // Assign subjects to teacher
  assignSubjects(teacherId) {
    console.log('Assign subjects to teacher:', teacherId);
  },

  // Delete teacher
  async deleteTeacher(teacherId) {
    if (!confirm('Are you sure you want to delete this teacher? This action cannot be undone.')) {
      return;
    }

    try {
      // Use the API service
      const result = await TeachersAPI.delete(teacherId);

      if (result.success) {
        AIMSDesignSystem.notifications.show('Teacher deleted successfully', 'success');
        await TeacherManagementComponents.loadInitialData();
        this.populateTeachersTable();
      } else {
        AIMSDesignSystem.notifications.show(result.message || 'Failed to delete teacher', 'error');
      }
    } catch (error) {
      console.error('Delete error:', error);
      AIMSDesignSystem.notifications.show('Failed to delete teacher', 'error');
    }
  },

  // Export teachers
  exportTeachers() {
    console.log('Export teachers');
  }
};

// Export components to global scope
window.RegisterTeacherComponent = RegisterTeacherComponent;
window.ManageTeachersComponent = ManageTeachersComponent;
window.TeacherManagementComponents = TeacherManagementComponents;
