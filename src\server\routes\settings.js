const express = require('express');
const { executeQuery } = require('../../database/connection');
const { authenticateToken } = require('./auth');

const router = express.Router();

// Get all school settings
router.get('/school', authenticateToken, async (req, res) => {
  try {
    const query = `
      SELECT 
        id,
        setting_key,
        setting_value,
        setting_type,
        category,
        description,
        is_editable,
        created_at,
        updated_at
      FROM school_settings 
      ORDER BY category, setting_key
    `;
    
    const result = await executeQuery(query);
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to fetch school settings',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Get school settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Get school settings by category
router.get('/school/category/:category', authenticateToken, async (req, res) => {
  try {
    const { category } = req.params;
    
    const query = `
      SELECT 
        id,
        setting_key,
        setting_value,
        setting_type,
        category,
        description,
        is_editable,
        created_at,
        updated_at
      FROM school_settings 
      WHERE category = ?
      ORDER BY setting_key
    `;
    
    const result = await executeQuery(query, [category]);
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to fetch school settings by category',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Get school settings by category error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Get school setting by key
router.get('/school/key/:key', authenticateToken, async (req, res) => {
  try {
    const { key } = req.params;
    
    const query = `
      SELECT 
        id,
        setting_key,
        setting_value,
        setting_type,
        category,
        description,
        is_editable,
        created_at,
        updated_at
      FROM school_settings 
      WHERE setting_key = ?
    `;
    
    const result = await executeQuery(query, [key]);
    
    if (result.success) {
      if (result.data.length > 0) {
        res.json({
          success: true,
          data: result.data[0]
        });
      } else {
        res.status(404).json({
          success: false,
          message: 'Setting not found'
        });
      }
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to fetch school setting',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Get school setting by key error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Update school setting by key
router.put('/school/key/:key', authenticateToken, async (req, res) => {
  try {
    const { key } = req.params;
    const { value } = req.body;
    
    if (value === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Setting value is required'
      });
    }
    
    // First check if setting exists and is editable
    const checkQuery = `
      SELECT id, is_editable 
      FROM school_settings 
      WHERE setting_key = ?
    `;
    
    const checkResult = await executeQuery(checkQuery, [key]);
    
    if (!checkResult.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to check setting',
        error: checkResult.error
      });
    }
    
    if (checkResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Setting not found'
      });
    }
    
    if (!checkResult.data[0].is_editable) {
      return res.status(403).json({
        success: false,
        message: 'This setting is not editable'
      });
    }
    
    // Update the setting
    const updateQuery = `
      UPDATE school_settings 
      SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
      WHERE setting_key = ?
    `;
    
    const result = await executeQuery(updateQuery, [value, key]);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'Setting updated successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to update setting',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Update school setting error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Update multiple school settings (bulk update)
router.put('/school/bulk', authenticateToken, async (req, res) => {
  try {
    const { settings } = req.body;

    if (!settings || !Array.isArray(settings)) {
      return res.status(400).json({
        success: false,
        message: 'Settings array is required'
      });
    }

    // Validate all settings first
    for (const setting of settings) {
      if (!setting.key || setting.value === undefined) {
        return res.status(400).json({
          success: false,
          message: 'Each setting must have key and value'
        });
      }
    }

    // Check if all settings exist and are editable
    const keys = settings.map(s => s.key);
    const placeholders = keys.map(() => '?').join(',');
    const checkQuery = `
      SELECT setting_key, is_editable
      FROM school_settings
      WHERE setting_key IN (${placeholders})
    `;

    const checkResult = await executeQuery(checkQuery, keys);

    if (!checkResult.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to validate settings',
        error: checkResult.error
      });
    }

    const existingSettings = checkResult.data;
    const nonEditableSettings = existingSettings.filter(s => !s.is_editable);

    if (nonEditableSettings.length > 0) {
      return res.status(403).json({
        success: false,
        message: `The following settings are not editable: ${nonEditableSettings.map(s => s.setting_key).join(', ')}`
      });
    }

    if (existingSettings.length !== settings.length) {
      const foundKeys = existingSettings.map(s => s.setting_key);
      const missingKeys = keys.filter(k => !foundKeys.includes(k));
      return res.status(404).json({
        success: false,
        message: `The following settings were not found: ${missingKeys.join(', ')}`
      });
    }

    // Update all settings
    const updatePromises = settings.map(setting => {
      const updateQuery = `
        UPDATE school_settings
        SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
        WHERE setting_key = ?
      `;
      return executeQuery(updateQuery, [setting.value, setting.key]);
    });

    const results = await Promise.all(updatePromises);
    const failedUpdates = results.filter(r => !r.success);

    if (failedUpdates.length > 0) {
      return res.status(500).json({
        success: false,
        message: 'Some settings failed to update',
        errors: failedUpdates.map(r => r.error)
      });
    }

    res.json({
      success: true,
      message: `Successfully updated ${settings.length} settings`
    });

  } catch (error) {
    console.error('Bulk update school settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Get all setting categories
router.get('/school/categories', authenticateToken, async (req, res) => {
  try {
    const query = `
      SELECT DISTINCT category
      FROM school_settings
      ORDER BY category
    `;

    const result = await executeQuery(query);

    if (result.success) {
      const categories = result.data.map(row => row.category);
      res.json({
        success: true,
        data: categories
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to fetch setting categories',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Get setting categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

module.exports = router;
