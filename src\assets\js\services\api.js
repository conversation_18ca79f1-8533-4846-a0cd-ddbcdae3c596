// AIMS API Service
// Centralized API service using environment configuration

// Use global Config 

class APIService {
  constructor() {
    this.baseURL = window.AIMSConfig ? window.AIMSConfig.getApiUrl() : 'http://localhost:3001/api';
    this.timeout = window.AIMSConfig ? window.AIMSConfig.get('api.timeout', 30000) : 30000;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  // Generic request method
  async request(endpoint, options = {}) {
    const url = window.AIMSConfig ? window.AIMSConfig.getApiUrl(endpoint) : `http://localhost:3001/api${endpoint}`;

    // Get authentication token from localStorage
    const token = localStorage.getItem('aims_token');
    const authHeaders = token ? { 'Authorization': `Bearer ${token}` } : {};

    const config = {
      method: 'GET',
      headers: { ...this.defaultHeaders, ...authHeaders, ...options.headers },
      ...options
    };

    // Add timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    config.signal = controller.signal;

    try {
      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.log(`API Request: ${config.method} ${url}`, config);
      }

      const response = await fetch(url, config);
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (window.AIMSConfig && window.AIMSConfig.get('development.debugMode')) {
        console.log(`API Response: ${config.method} ${url}`, data);
      }

      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      
      console.error(`API Error: ${config.method} ${url}`, error);
      throw error;
    }
  }

  // GET request
  async get(endpoint, params = {}) {
    let url = endpoint;

    // Add query parameters if provided
    if (Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
          searchParams.append(key, params[key]);
        }
      });
      url += '?' + searchParams.toString();
    }

    return this.request(url);
  }

  // POST request
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  // PUT request
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  // PATCH request
  async patch(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data)
    });
  }

  // DELETE request
  async delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE'
    });
  }

  // File upload
  async uploadFile(endpoint, file, additionalData = {}) {
    const formData = new FormData();
    formData.append('file', file);
    
    Object.keys(additionalData).forEach(key => {
      formData.append(key, additionalData[key]);
    });

    return this.request(endpoint, {
      method: 'POST',
      headers: {}, // Let browser set Content-Type for FormData
      body: formData
    });
  }

  // Bulk operations
  async bulkOperation(endpoint, operation, data) {
    return this.post(endpoint, {
      operation,
      data
    });
  }
}

// Create singleton instance
const API = new APIService();

// Specific API methods for AIMS entities
const StudentsAPI = {
  getAll: (params = {}) => API.get('/students', params),
  getById: (id) => API.get(`/students/${id}`),
  create: (data) => API.post('/students', data),
  update: (id, data) => API.put(`/students/${id}`, data),
  delete: (id) => API.delete(`/students/${id}`),
  register: (data) => API.post('/students/register', data),
  enroll: (data) => API.post('/students/enroll', data),
  getEnrollments: (params = {}) => API.get('/students/enrollments', params),
  deleteEnrollment: (id) => API.delete(`/students/enrollments/${id}`),
  uploadPhoto: (id, file) => API.uploadFile(`/students/${id}/photo`, file)
};

const TeachersAPI = {
  getAll: (params = {}) => API.get('/teachers', params),
  getById: (id) => API.get(`/teachers/${id}`),
  create: (data) => API.post('/teachers', data),
  update: (id, data) => API.put(`/teachers/${id}`, data),
  delete: (id) => API.delete(`/teachers/${id}`),
  uploadPhoto: (id, file) => API.uploadFile(`/teachers/${id}/photo`, file)
};

const SubjectsAPI = {
  // O-Level subjects
  oLevel: {
    getAll: (params = {}) => API.get('/academic/subjects/o-level', params),
    getById: (id) => API.get(`/academic/subjects/o-level/${id}`),
    create: (data) => API.post('/academic/subjects/o-level', data),
    update: (id, data) => API.put(`/academic/subjects/o-level/${id}`, data),
    delete: (id) => API.delete(`/academic/subjects/o-level/${id}`),
    toggleStatus: (id) => API.patch(`/academic/subjects/o-level/${id}/toggle-status`)
  },
  // A-Level subjects
  aLevel: {
    getAll: (params = {}) => API.get('/academic/subjects/a-level', params),
    getById: (id) => API.get(`/academic/subjects/a-level/${id}`),
    create: (data) => API.post('/academic/subjects/a-level', data),
    update: (id, data) => API.put(`/academic/subjects/a-level/${id}`, data),
    delete: (id) => API.delete(`/academic/subjects/a-level/${id}`),
    toggleStatus: (id) => API.patch(`/academic/subjects/a-level/${id}/toggle-status`)
  },
  // Legacy methods for backward compatibility
  getAll: (params = {}) => {
    console.warn('SubjectsAPI.getAll is deprecated. Use SubjectsAPI.oLevel.getAll or SubjectsAPI.aLevel.getAll');
    return API.get('/academic/subjects/o-level', params);
  },
  getById: (id) => {
    console.warn('SubjectsAPI.getById is deprecated. Use SubjectsAPI.oLevel.getById or SubjectsAPI.aLevel.getById');
    return API.get(`/academic/subjects/o-level/${id}`);
  },
  create: (data) => {
    console.warn('SubjectsAPI.create is deprecated. Use SubjectsAPI.oLevel.create or SubjectsAPI.aLevel.create');
    return API.post('/academic/subjects/o-level', data);
  },
  update: (id, data) => {
    console.warn('SubjectsAPI.update is deprecated. Use SubjectsAPI.oLevel.update or SubjectsAPI.aLevel.update');
    return API.put(`/academic/subjects/o-level/${id}`, data);
  },
  delete: (id) => {
    console.warn('SubjectsAPI.delete is deprecated. Use SubjectsAPI.oLevel.delete or SubjectsAPI.aLevel.delete');
    return API.delete(`/academic/subjects/o-level/${id}`);
  },
  toggleStatus: (id) => {
    console.warn('SubjectsAPI.toggleStatus is deprecated. Use SubjectsAPI.oLevel.toggleStatus or SubjectsAPI.aLevel.toggleStatus');
    return API.patch(`/academic/subjects/o-level/${id}/toggle-status`);
  }
};

const ClassesAPI = {
  getAll: (params = {}) => API.get('/academic/classes', params),
  getById: (id) => API.get(`/academic/classes/${id}`),
  create: (data) => API.post('/academic/classes', data),
  update: (id, data) => API.put(`/academic/classes/${id}`, data),
  delete: (id) => API.delete(`/academic/classes/${id}`)
};

const StreamsAPI = {
  getAll: (params = {}) => API.get('/academic/streams', params),
  getById: (id) => API.get(`/academic/streams/${id}`),
  create: (data) => API.post('/academic/streams', data),
  update: (id, data) => API.put(`/academic/streams/${id}`, data),
  delete: (id) => API.delete(`/academic/streams/${id}`),
  toggleStatus: (id) => API.patch(`/academic/streams/${id}/toggle-status`)
};

const CombinationsAPI = {
  getAll: (params = {}) => API.get('/a-level-combinations', params),
  getById: (id) => API.get(`/a-level-combinations/${id}`),
  create: (data) => API.post('/a-level-combinations', data),
  update: (id, data) => API.put(`/a-level-combinations/${id}`, data),
  delete: (id) => API.delete(`/a-level-combinations/${id}`),
  toggleStatus: (id) => API.patch(`/a-level-combinations/${id}/toggle-status`),
  getSubjects: (id) => API.get(`/a-level-combinations/${id}/subjects`)
};

const AssignmentsAPI = {
  getAll: (params = {}) => API.get('/teacher-assignments', params),
  getById: (id) => API.get(`/teacher-assignments/${id}`),
  create: (data) => API.post('/teacher-assignments', data),
  bulkCreate: (assignments) => API.post('/teacher-assignments/bulk', { assignments }),
  update: (id, data) => API.put(`/teacher-assignments/${id}`, data),
  delete: (id) => API.delete(`/teacher-assignments/${id}`),
  toggleStatus: (id) => API.patch(`/teacher-assignments/${id}/toggle-status`)
};

const AssessmentsAPI = {
  // O-Level continuous assessments
  oLevel: {
    getAll: (params = {}) => API.get('/assessments/continuous/o-level', params),
    getById: (id) => API.get(`/assessments/continuous/o-level/${id}`),
    create: (data) => API.post('/assessments/continuous/o-level', data),
    update: (id, data) => API.put(`/assessments/continuous/o-level/${id}`, data),
    delete: (id) => API.delete(`/assessments/continuous/o-level/${id}`)
  },
  // A-Level continuous assessments
  aLevel: {
    getAll: (params = {}) => API.get('/assessments/continuous/a-level', params),
    getById: (id) => API.get(`/assessments/continuous/a-level/${id}`),
    create: (data) => API.post('/assessments/continuous/a-level', data),
    update: (id, data) => API.put(`/assessments/continuous/a-level/${id}`, data),
    delete: (id) => API.delete(`/assessments/continuous/a-level/${id}`)
  },
  // Examinations
  examinations: {
    oLevel: {
      getAll: (params = {}) => API.get('/assessments/examinations/o-level', params)
    },
    aLevel: {
      getAll: (params = {}) => API.get('/assessments/examinations/a-level', params)
    }
  },
  // Statistics
  getStatistics: (params = {}) => API.get('/assessments/statistics', params),
  // Legacy methods for backward compatibility
  getAll: (params = {}) => {
    console.warn('AssessmentsAPI.getAll is deprecated. Use AssessmentsAPI.oLevel.getAll or AssessmentsAPI.aLevel.getAll');
    return API.get('/assessments/continuous/o-level', params);
  },
  getById: (id) => {
    console.warn('AssessmentsAPI.getById is deprecated. Use AssessmentsAPI.oLevel.getById or AssessmentsAPI.aLevel.getById');
    return API.get(`/assessments/continuous/o-level/${id}`);
  },
  create: (data) => {
    console.warn('AssessmentsAPI.create is deprecated. Use AssessmentsAPI.oLevel.create or AssessmentsAPI.aLevel.create');
    return API.post('/assessments/continuous/o-level', data);
  },
  update: (id, data) => {
    console.warn('AssessmentsAPI.update is deprecated. Use AssessmentsAPI.oLevel.update or AssessmentsAPI.aLevel.update');
    return API.put(`/assessments/continuous/o-level/${id}`, data);
  },
  delete: (id) => {
    console.warn('AssessmentsAPI.delete is deprecated. Use AssessmentsAPI.oLevel.delete or AssessmentsAPI.aLevel.delete');
    return API.delete(`/assessments/continuous/o-level/${id}`);
  },
  getStudents: (id) => {
    console.warn('AssessmentsAPI.getStudents is deprecated and no longer supported');
    return Promise.reject(new Error('Method no longer supported'));
  }
};

const CAScoresAPI = {
  getAll: (params = {}) => API.get('/ca-scores', params),
  create: (scores) => API.post('/ca-scores', { scores }),
  update: (id, data) => API.put(`/ca-scores/${id}`, data),
  delete: (id) => API.delete(`/ca-scores/${id}`)
};

const ExamGradesAPI = {
  getAll: (params = {}) => API.get('/exam-grades', params),
  getStudents: (params) => API.get('/exam-grades/students', params),
  create: (grades) => API.post('/exam-grades', { grades }),
  update: (id, data) => API.put(`/exam-grades/${id}`, data),
  delete: (id) => API.delete(`/exam-grades/${id}`)
};

const GradeBoundariesAPI = {
  // O-Level grade boundaries
  oLevel: {
    getAll: (params = {}) => API.get('/academic/grade-boundaries/o-level', params),
    update: (boundaries) => API.put('/academic/grade-boundaries/o-level', { boundaries }),
    getHistory: () => API.get('/academic/grade-boundaries/o-level/history')
  },
  // A-Level Principal grade boundaries
  aLevelPrincipal: {
    getAll: (params = {}) => API.get('/academic/grade-boundaries/a-level-principal', params),
    update: (boundaries) => API.put('/academic/grade-boundaries/a-level-principal', { boundaries }),
    getHistory: () => API.get('/academic/grade-boundaries/a-level-principal/history')
  },
  // A-Level Subsidiary grade boundaries (fixed boundaries)
  aLevelSubsidiary: {
    getAll: (params = {}) => API.get('/academic/grade-boundaries/a-level-subsidiary', params)
  },
  // Legacy methods for backward compatibility
  getAll: (params = {}) => {
    console.warn('GradeBoundariesAPI.getAll is deprecated. Use specific level methods');
    return API.get('/academic/grade-boundaries/o-level', params);
  },
  update: (boundaries) => {
    console.warn('GradeBoundariesAPI.update is deprecated. Use specific level methods');
    return API.put('/academic/grade-boundaries/o-level', { boundaries });
  },
  getHistory: () => {
    console.warn('GradeBoundariesAPI.getHistory is deprecated. Use specific level methods');
    return API.get('/academic/grade-boundaries/o-level/history');
  }
};

const PromotionsAPI = {
  getEligibleStudents: (params = {}) => API.get('/promotions/eligible-students', params),
  getTargetClasses: (params = {}) => API.get('/promotions/target-classes', params),
  promote: (data) => API.post('/promotions/promote', data),
  getHistory: (params = {}) => API.get('/promotions/history', params),
  getStats: (params = {}) => API.get('/promotions/stats', params)
};

const AcademicYearsAPI = {
  getAll: (params = {}) => API.get('/academic/years', params),
  getById: (id) => API.get(`/academic/years/${id}`),
  create: (data) => API.post('/academic/years', data),
  update: (id, data) => API.put(`/academic/years/${id}`, data),
  delete: (id) => API.delete(`/academic/years/${id}`)
};

const TermsAPI = {
  getAll: (params = {}) => API.get('/academic/terms', params),
  getById: (id) => API.get(`/academic/terms/${id}`),
  create: (data) => API.post('/academic/terms', data),
  update: (id, data) => API.put(`/academic/terms/${id}`, data),
  delete: (id) => API.delete(`/academic/terms/${id}`)
};

const LevelsAPI = {
  getAll: (params = {}) => API.get('/academic/levels', params)
};

const ClassLevelsAPI = {
  getAll: (params = {}) => API.get('/academic/class-levels', params)
};

const ReportsAPI = {
  generateReportCards: (params) => API.post('/reports/report-cards', params),
  getReportCard: (studentId, params) => API.get(`/reports/report-cards/${studentId}`, params),
  exportData: (type, params) => API.get(`/reports/export/${type}`, params)
};

const DashboardAPI = {
  getStats: (params = {}) => API.get('/dashboard/stats', params),
  getTrends: (params = {}) => API.get('/dashboard/trends', params),
  getSubjectPerformance: (params = {}) => API.get('/dashboard/subject-performance', params)
};

const AcademicAPI = {
  getCurrentContext: () => API.get('/academic/current-context'),
  getYearOptions: () => API.get('/academic/year-options'),
  getClasses: (params = {}) => API.get('/academic/classes', params)
};

const SchoolSettingsAPI = {
  getAll: (params = {}) => API.get('/settings/school', params),
  getByCategory: (category) => API.get(`/settings/school/category/${category}`),
  getByKey: (key) => API.get(`/settings/school/key/${key}`),
  update: (key, value) => API.put(`/settings/school/key/${key}`, { value }),
  updateMultiple: (settings) => API.put('/settings/school/bulk', { settings }),
  getCategories: () => API.get('/settings/school/categories')
};

const PageDataAPI = {
  getRegistrationFormData: (type) => API.get(`/${type}/registration-form-data`),
  getReportsFormData: (params = {}) => API.get('/reports/form-data', params)
};

const AuthAPI = {
  login: (credentials) => API.post('/auth/login', credentials),
  logout: () => API.post('/auth/logout'),
  validateToken: (token) => API.post('/auth/validate', { token }),
  refreshToken: () => API.post('/auth/refresh')
};

// All API services available globally
window.API = API;
window.StudentsAPI = StudentsAPI;
window.TeachersAPI = TeachersAPI;
window.ClassesAPI = ClassesAPI;
window.StreamsAPI = StreamsAPI;
window.SubjectsAPI = SubjectsAPI;
window.CombinationsAPI = CombinationsAPI;
window.AssessmentsAPI = AssessmentsAPI;
window.CAScoresAPI = CAScoresAPI;
window.ExamGradesAPI = ExamGradesAPI;
window.AssignmentsAPI = AssignmentsAPI;
window.GradeBoundariesAPI = GradeBoundariesAPI;
window.PromotionsAPI = PromotionsAPI;
window.AcademicYearsAPI = AcademicYearsAPI;
window.TermsAPI = TermsAPI;
window.LevelsAPI = LevelsAPI;
window.ClassLevelsAPI = ClassLevelsAPI;
window.ReportsAPI = ReportsAPI;
window.DashboardAPI = DashboardAPI;
window.AcademicAPI = AcademicAPI;
window.SchoolSettingsAPI = SchoolSettingsAPI;
window.PageDataAPI = PageDataAPI;
window.AuthAPI = AuthAPI;

console.log('✅ All API services loaded and available globally');
