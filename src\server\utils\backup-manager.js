const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');
const { executeQuery } = require('../../database/connection');

class BackupManager {
  constructor() {
    this.backupDir = path.join(__dirname, '../../backups');
    this.ensureBackupDirectory();
  }

  async ensureBackupDirectory() {
    try {
      await fs.access(this.backupDir);
    } catch (error) {
      await fs.mkdir(this.backupDir, { recursive: true });
    }
  }

  // Create database backup
  async createBackup(options = {}) {
    try {
      const {
        name = `aims_backup_${new Date().toISOString().split('T')[0]}_${Date.now()}`,
        description = 'Automated backup',
        type = 'full',
        includeData = true
      } = options;

      const timestamp = new Date().toISOString();
      const fileName = `${name}.sql`;
      const filePath = path.join(this.backupDir, fileName);

      // Create backup record in database
      const backupRecord = await this.createBackupRecord(name, description, type, fileName);
      
      if (!backupRecord.success) {
        throw new Error('Failed to create backup record');
      }

      const backupId = backupRecord.insertId;

      // Generate mysqldump command
      const dumpOptions = [
        '--host=' + (process.env.DB_HOST || 'localhost'),
        '--port=' + (process.env.DB_PORT || '3306'),
        '--user=' + (process.env.DB_USER || 'root'),
        '--single-transaction',
        '--routines',
        '--triggers'
      ];

      if (process.env.DB_PASSWORD) {
        dumpOptions.push('--password=' + process.env.DB_PASSWORD);
      }

      if (!includeData) {
        dumpOptions.push('--no-data');
      }

      dumpOptions.push(process.env.DB_NAME || 'aims_db');

      // Execute mysqldump
      const dumpResult = await this.executeMysqlDump(dumpOptions, filePath);

      if (dumpResult.success) {
        // Get file size
        const stats = await fs.stat(filePath);
        const fileSize = stats.size;

        // Update backup record
        await this.updateBackupRecord(backupId, {
          status: 'completed',
          file_size: fileSize,
          completed_at: new Date()
        });

        return {
          success: true,
          backup_id: backupId,
          file_path: filePath,
          file_size: fileSize,
          message: 'Backup created successfully'
        };
      } else {
        // Update backup record as failed
        await this.updateBackupRecord(backupId, {
          status: 'failed',
          error_message: dumpResult.error
        });

        throw new Error(dumpResult.error);
      }

    } catch (error) {
      console.error('Backup creation error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Execute mysqldump command
  async executeMysqlDump(options, outputPath) {
    return new Promise((resolve) => {
      const mysqldump = spawn('mysqldump', options);
      const writeStream = require('fs').createWriteStream(outputPath);
      
      let errorOutput = '';

      mysqldump.stdout.pipe(writeStream);
      
      mysqldump.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      mysqldump.on('close', (code) => {
        writeStream.end();
        
        if (code === 0) {
          resolve({ success: true });
        } else {
          resolve({ 
            success: false, 
            error: errorOutput || `mysqldump exited with code ${code}` 
          });
        }
      });

      mysqldump.on('error', (error) => {
        writeStream.end();
        resolve({ 
          success: false, 
          error: error.message 
        });
      });
    });
  }

  // Restore database from backup
  async restoreBackup(backupId, options = {}) {
    try {
      const { confirmRestore = false } = options;

      if (!confirmRestore) {
        return {
          success: false,
          error: 'Restore confirmation required'
        };
      }

      // Get backup information
      const backupQuery = 'SELECT * FROM system_backups WHERE id = ?';
      const backupResult = await executeQuery(backupQuery, [backupId]);

      if (!backupResult.success || backupResult.data.length === 0) {
        throw new Error('Backup not found');
      }

      const backup = backupResult.data[0];
      const backupFilePath = path.join(this.backupDir, backup.file_name);

      // Check if backup file exists
      try {
        await fs.access(backupFilePath);
      } catch (error) {
        throw new Error('Backup file not found');
      }

      // Create pre-restore backup
      const preRestoreBackup = await this.createBackup({
        name: `pre_restore_${Date.now()}`,
        description: `Automatic backup before restoring ${backup.name}`,
        type: 'pre_restore'
      });

      // Execute mysql restore
      const restoreResult = await this.executeMysqlRestore(backupFilePath);

      if (restoreResult.success) {
        // Update backup record
        await this.updateBackupRecord(backupId, {
          last_restored_at: new Date()
        });

        return {
          success: true,
          message: 'Database restored successfully',
          pre_restore_backup: preRestoreBackup.backup_id
        };
      } else {
        throw new Error(restoreResult.error);
      }

    } catch (error) {
      console.error('Restore error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Execute mysql restore command
  async executeMysqlRestore(backupFilePath) {
    return new Promise((resolve) => {
      const mysqlOptions = [
        '--host=' + (process.env.DB_HOST || 'localhost'),
        '--port=' + (process.env.DB_PORT || '3306'),
        '--user=' + (process.env.DB_USER || 'root')
      ];

      if (process.env.DB_PASSWORD) {
        mysqlOptions.push('--password=' + process.env.DB_PASSWORD);
      }

      mysqlOptions.push(process.env.DB_NAME || 'aims_db');

      const mysql = spawn('mysql', mysqlOptions);
      const readStream = require('fs').createReadStream(backupFilePath);
      
      let errorOutput = '';

      readStream.pipe(mysql.stdin);
      
      mysql.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      mysql.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true });
        } else {
          resolve({ 
            success: false, 
            error: errorOutput || `mysql exited with code ${code}` 
          });
        }
      });

      mysql.on('error', (error) => {
        resolve({ 
          success: false, 
          error: error.message 
        });
      });
    });
  }

  // Create backup record in database
  async createBackupRecord(name, description, type, fileName) {
    const query = `
      INSERT INTO system_backups (
        name, description, backup_type, file_name, status, created_at
      ) VALUES (?, ?, ?, ?, 'in_progress', NOW())
    `;

    return await executeQuery(query, [name, description, type, fileName]);
  }

  // Update backup record
  async updateBackupRecord(backupId, updates) {
    const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);
    values.push(backupId);

    const query = `UPDATE system_backups SET ${setClause} WHERE id = ?`;
    return await executeQuery(query, values);
  }

  // Get backup list
  async getBackupList(options = {}) {
    try {
      const { limit = 50, type, status } = options;
      
      let query = `
        SELECT id, name, description, backup_type, file_name, file_size,
               status, created_at, completed_at, last_restored_at, error_message
        FROM system_backups
        WHERE 1=1
      `;
      
      const params = [];
      
      if (type) {
        query += ' AND backup_type = ?';
        params.push(type);
      }
      
      if (status) {
        query += ' AND status = ?';
        params.push(status);
      }
      
      query += ' ORDER BY created_at DESC LIMIT ?';
      params.push(limit);
      
      const result = await executeQuery(query, params);
      
      if (result.success) {
        // Add file existence check
        for (let backup of result.data) {
          const filePath = path.join(this.backupDir, backup.file_name);
          try {
            await fs.access(filePath);
            backup.file_exists = true;
          } catch (error) {
            backup.file_exists = false;
          }
        }
      }
      
      return result;
    } catch (error) {
      console.error('Get backup list error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Delete backup
  async deleteBackup(backupId) {
    try {
      // Get backup information
      const backupQuery = 'SELECT * FROM system_backups WHERE id = ?';
      const backupResult = await executeQuery(backupQuery, [backupId]);

      if (!backupResult.success || backupResult.data.length === 0) {
        throw new Error('Backup not found');
      }

      const backup = backupResult.data[0];
      const backupFilePath = path.join(this.backupDir, backup.file_name);

      // Delete file if exists
      try {
        await fs.unlink(backupFilePath);
      } catch (error) {
        console.warn('Backup file not found for deletion:', backupFilePath);
      }

      // Delete database record
      const deleteQuery = 'DELETE FROM system_backups WHERE id = ?';
      const deleteResult = await executeQuery(deleteQuery, [backupId]);

      if (!deleteResult.success) {
        throw new Error(deleteResult.error);
      }

      return {
        success: true,
        message: 'Backup deleted successfully'
      };

    } catch (error) {
      console.error('Delete backup error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Schedule automatic backups
  async scheduleBackup(schedule = 'daily') {
    // This would integrate with a job scheduler like node-cron
    // For now, just return the configuration
    return {
      success: true,
      schedule: schedule,
      message: 'Backup scheduling configured'
    };
  }

  // Verify backup integrity
  async verifyBackup(backupId) {
    try {
      const backupQuery = 'SELECT * FROM system_backups WHERE id = ?';
      const backupResult = await executeQuery(backupQuery, [backupId]);

      if (!backupResult.success || backupResult.data.length === 0) {
        throw new Error('Backup not found');
      }

      const backup = backupResult.data[0];
      const backupFilePath = path.join(this.backupDir, backup.file_name);

      // Check file existence
      const stats = await fs.stat(backupFilePath);
      
      // Basic integrity checks
      const isValid = stats.size > 0 && stats.size === backup.file_size;

      return {
        success: true,
        is_valid: isValid,
        file_size: stats.size,
        expected_size: backup.file_size,
        last_modified: stats.mtime
      };

    } catch (error) {
      console.error('Verify backup error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = BackupManager;
